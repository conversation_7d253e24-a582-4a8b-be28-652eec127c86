<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\User;
use App\Models\StateData;
use App\Models\DistrictStateCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EventController extends Controller
{
    /**
     * Display the events page.
     */
    public function index()
    {
        return view('district-state-coordinator.events.index');
    }

    /**
     * Get events in the coordinator's districts.
     */
    public function getEvents()
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get action plans in these districts
            $events = DB::table('action_plans')
                ->select([
                    'action_plans.id',
                    'action_plans.title',
                    'action_plans.type',
                    'action_plans.location',
                    'action_plans.planned_date as start_date',
                    'action_plans.expected_participants',
                    'action_plans.status',
                    'action_plans.cancellation_reason',
                    'action_plans.created_at',
                    'action_plans.updated_at',
                    'users.name as scientist_name',
                    'users.email as scientist_email',
                    'state_data.state',
                    'state_data.district',
                    DB::raw('(COALESCE(male_participants, 0) + COALESCE(female_participants, 0) + COALESCE(transgender_participants, 0)) as actual_participants'),
                    'zc_pvent_preparedness_feedback',
                    'zc_communication_effectiveness_feedback',
                    'zc_participant_engagement_feedback',
                    'zc_timeline_adherence_feedback',
                    'zc_remark'
                ])
                ->join('users', 'action_plans.scientist_id', '=', 'users.id')
                ->join('state_data', 'action_plans.district_id', '=', 'state_data.id')
                ->whereIn('action_plans.district_id', $districtIds)
                ->orderBy('action_plans.planned_date', 'desc')
                ->get();

            return response()->json($events);
        } catch (\Exception $e) {
            Log::error('Error getting events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get events'], 500);
        }
    }

    /**
     * Get event details.
     */
    public function getEventDetails($id)
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get the event from action_plans table
            $event = DB::table('action_plans')
                ->select([
                    'action_plans.id',
                    'action_plans.title',
                    'action_plans.type',
                    'action_plans.location',
                    'action_plans.planned_date as start_date',
                    'action_plans.expected_participants',
                    'action_plans.status',
                    'action_plans.male_participants',
                    'action_plans.female_participants',
                    'action_plans.transgender_participants',
                    DB::raw('(COALESCE(male_participants, 0) + COALESCE(female_participants, 0) + COALESCE(transgender_participants, 0)) as actual_participants'),
                    'users.name as scientist_name',
                    'users.email as scientist_email',
                    'state_data.state',
                    'state_data.district'
                ])
                ->join('users', 'action_plans.scientist_id', '=', 'users.id')
                ->join('state_data', 'action_plans.district_id', '=', 'state_data.id')
                ->where('action_plans.id', $id)
                ->whereIn('action_plans.district_id', $districtIds)
                ->first();

            if (!$event) {
                return response()->json(['error' => 'Event not found or not in your districts'], 404);
            }

            // Format the response to match expected structure
            $formattedEvent = [
                'id' => $event->id,
                'title' => $event->title,
                'type' => $event->type,
                'location' => $event->location,
                'start_date' => $event->start_date,
                'status' => $event->status,
                'expected_participants' => $event->expected_participants,
                'actual_participants' => $event->actual_participants,
                'scientist' => [
                    'name' => $event->scientist_name,
                    'email' => $event->scientist_email
                ],
                'district' => [
                    'state' => $event->state,
                    'district' => $event->district
                ]
            ];

            return response()->json($formattedEvent);
        } catch (\Exception $e) {
            Log::error('Error getting event details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get event details'], 500);
        }
    }

    /**
     * Get events by scientist.
     */
    public function getEventsByScientist($id)
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get the scientist
            $scientist = User::where('id', $id)
                ->where('role', 'scientist')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            // Check if the scientist is assigned to any of the coordinator's districts
            $isAssigned = StateData::where('scientist', $scientist->email)
                ->whereIn('id', $districtIds)
                ->exists();

            if (!$isAssigned) {
                return response()->json(['error' => 'Scientist not found in your districts'], 404);
            }

            // Get action plans (events) for this scientist
            $events = DB::table('action_plans')
                ->select([
                    'id',
                    'title',
                    'planned_date as start_date',
                    'location',
                    'status',
                    'expected_participants',
                    DB::raw('(COALESCE(male_participants, 0) + COALESCE(female_participants, 0) + COALESCE(transgender_participants, 0)) as actual_participants'),
                    'scientist_id',
                    'created_at'
                ])
                ->where('scientist_id', $id)
                ->orderBy('planned_date', 'desc')
                ->get();

            return response()->json($events);
        } catch (\Exception $e) {
            Log::error('Error getting events by scientist: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get events'], 500);
        }
    }
}
