/**
 * District User Manager JavaScript
 * Handles the state and district dropdowns for the admin district user management page
 */
document.addEventListener('DOMContentLoaded', function() {
    const stateSelect = document.getElementById('stateSelect');
    const districtSelect = document.getElementById('districtSelect');
    const districtUserForm = document.getElementById('districtUserForm');
    const saveDistrictUserForm = document.getElementById('saveDistrictUserForm');
    const passwordHelpText = document.getElementById('passwordHelpText');

    // Password toggle functionality
    const toggleAdminPassword = document.getElementById('toggleAdminPassword');
    if (toggleAdminPassword) {
        toggleAdminPassword.addEventListener('click', function() {
            const passwordField = document.getElementById('scientistPassword');
            const passwordIcon = document.getElementById('adminPasswordIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
            }
        });
    }

    // Load states from the JSON file
    loadStates();

    // State change: Load districts for the selected state
    stateSelect.addEventListener('change', function() {
        districtSelect.innerHTML = '<option value="">-- Select District --</option>';
        districtSelect.disabled = !this.value;
        districtUserForm.style.display = 'none';

        if (this.value) {
            document.getElementById('stateName').value = this.value;
            loadDistricts(this.value);
        }
    });

    // District change: Show form and populate data
    districtSelect.addEventListener('change', function() {
        if (this.value) {
            const selectedOption = this.options[this.selectedIndex];
            document.getElementById('districtId').value = this.value;
            document.getElementById('districtName').value = selectedOption.text;
            document.getElementById('districtStatus').value = selectedOption.dataset.status || '';
            document.getElementById('scientistName').value = selectedOption.dataset.scientistName || '';
            document.getElementById('scientistEmail').value = selectedOption.dataset.scientistEmail || '';
            document.getElementById('scientistPhone').value = '';
            document.getElementById('isNew').value = selectedOption.dataset.scientistName ? 'false' : 'true';

            // Reset password field
            document.getElementById('scientistPassword').value = '';
            document.getElementById('scientistPassword').required = true;
            passwordHelpText.textContent = 'Password is required for new scientist';

            // Fetch additional scientist details if exists
            fetch(`/admin/get-district-details/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.scientist) {
                        document.getElementById('scientistName').value = data.scientist.name || '';
                        document.getElementById('scientistEmail').value = data.scientist.email || '';
                        document.getElementById('scientistPhone').value = data.scientist.phone_number || '';
                        document.getElementById('userId').value = data.scientist.id || '';
                        document.getElementById('isNew').value = 'false';
                        document.getElementById('scientistPassword').required = false;
                        passwordHelpText.textContent = 'Leave blank to keep current password';
                    } else {
                        document.getElementById('userId').value = '';
                    }
                })
                .catch(error => {
                    console.error('Error loading district details:', error);
                });

            districtUserForm.style.display = 'block';
        } else {
            districtUserForm.style.display = 'none';
        }
    });

    // Function to load states from the JSON file
    function loadStates() {
        stateSelect.innerHTML = '<option value="">-- Select State --</option><option disabled>Loading states...</option>';

        fetch('/admin/get-states')
            .then(response => response.json())
            .then(states => {
                stateSelect.innerHTML = '<option value="">-- Select State --</option>';
                if (states && states.length > 0) {
                    states.forEach(state => {
                        const option = document.createElement('option');
                        option.value = state;
                        option.textContent = state;
                        stateSelect.appendChild(option);
                    });
                } else {
                    stateSelect.innerHTML += '<option disabled>No states found</option>';
                }
            })
            .catch(error => {
                console.error('Error loading states:', error);
                stateSelect.innerHTML = '<option value="">-- Select State --</option><option disabled>Error loading states</option>';
            });
    }

    // Function to load districts for a selected state
    function loadDistricts(stateName) {
        districtSelect.innerHTML = '<option value="">-- Select District --</option><option disabled>Loading districts...</option>';

        fetch(`/admin/get-districts/${encodeURIComponent(stateName)}`)
            .then(response => response.json())
            .then(districts => {
                districtSelect.innerHTML = '<option value="">-- Select District --</option>';
                if (districts && districts.length > 0) {
                    districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.name || district.district;
                        option.dataset.status = district.status || '';
                        option.dataset.scientistName = district.scientist_name || '';
                        option.dataset.scientistEmail = district.scientist || '';
                        districtSelect.appendChild(option);
                    });
                } else {
                    districtSelect.innerHTML += '<option disabled>No districts found</option>';
                }
            })
            .catch(error => {
                console.error('Error loading districts:', error);
                districtSelect.innerHTML = '<option value="">-- Select District --</option><option disabled>Error loading districts</option>';
            });
    }

    // Form submission
    if (saveDistrictUserForm) {
        saveDistrictUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const formDataObj = Object.fromEntries(formData);

            if (!formDataObj.district_id || !formDataObj.status || !formDataObj.name || !formDataObj.email || !formDataObj.phone_number) {
                alert('Please fill in all required fields including District Type');
                return;
            }

            if (formDataObj.is_new === 'true' && !formDataObj.password) {
                alert('Password is required for new scientist');
                return;
            }

            // Submit form data
            fetch('/admin/save-district-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(formDataObj)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('District user saved successfully');
                    // Refresh the district dropdown to show updated data
                    if (stateSelect.value) {
                        loadDistricts(stateSelect.value);
                    }
                } else {
                    alert('Error: ' + (data.message || 'Failed to save district user'));
                }
            })
            .catch(error => {
                console.error('Error saving district user:', error);
                alert('An error occurred while saving district user');
            });
        });
    }
});
