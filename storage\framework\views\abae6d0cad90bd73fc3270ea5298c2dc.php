<?php $__env->startSection('title', 'Upcoming Programs - <PERSON><PERSON> A<PERSON>hima<PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-600 to-indigo-700 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">Upcoming Programs This Week</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Join us for these exciting sericulture programs happening across different districts
            </p>
        </div>
    </div>
</div>

<!-- Programs Section -->
<div class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if($upcomingActionPlans->count() > 0): ?>
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($upcomingActionPlans->count()); ?> Programs Scheduled</h2>
                <p class="text-gray-600"><?php echo e(now()->startOfWeek()->format('M d')); ?> - <?php echo e(now()->endOfWeek()->format('M d, Y')); ?></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $upcomingActionPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $actionPlan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="p-6">
                        <!-- Program Type Badge -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                <?php if($actionPlan->type === 'training'): ?> bg-blue-100 text-blue-800
                                <?php elseif($actionPlan->type === 'field_visit'): ?> bg-green-100 text-green-800
                                <?php elseif($actionPlan->type === 'demonstration'): ?> bg-purple-100 text-purple-800
                                <?php else: ?> bg-orange-100 text-orange-800
                                <?php endif; ?>">
                                <?php echo e(ucfirst(str_replace('_', ' ', $actionPlan->type))); ?>

                            </span>
                            <span class="text-sm text-gray-500">
                                <?php echo e(\Carbon\Carbon::parse($actionPlan->planned_date)->format('M d, Y')); ?>

                            </span>
                        </div>

                        <!-- Program Title -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3"><?php echo e($actionPlan->title); ?></h3>

                        <!-- Date and Time -->
                        <div class="flex items-center text-gray-600 mb-3">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span><?php echo e(\Carbon\Carbon::parse($actionPlan->planned_date)->format('l, M d, Y')); ?></span>
                        </div>

                        <!-- Location -->
                        <div class="flex items-center text-gray-600 mb-3">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span><?php echo e($actionPlan->location); ?></span>
                        </div>

                        <!-- District -->
                        <div class="flex items-center text-gray-600 mb-3">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                            </svg>
                            <span><?php echo e($actionPlan->district->district ?? 'N/A'); ?>, <?php echo e($actionPlan->district->state ?? 'N/A'); ?></span>
                        </div>

                        <!-- Scientist -->
                        <div class="flex items-center text-gray-600 mb-4">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span><?php echo e($actionPlan->scientist->name ?? 'N/A'); ?></span>
                        </div>

                        <!-- Expected Participants -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">Expected Participants: <strong><?php echo e($actionPlan->expected_participants ?? 'TBD'); ?></strong></span>
                            <span class="text-xs text-gray-400">
                                <?php echo e(\Carbon\Carbon::parse($actionPlan->planned_date)->diffForHumans()); ?>

                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Back to Home -->
            <div class="text-center mt-12">
                <a href="<?php echo e(route('public.home')); ?>" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="mr-2 -ml-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Back to Home
                </a>
            </div>
        <?php else: ?>
            <!-- No Programs -->
            <div class="text-center py-16">
                <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h3 class="mt-6 text-2xl font-medium text-gray-900">No Programs Scheduled</h3>
                <p class="mt-2 text-gray-500 max-w-md mx-auto">
                    There are no sericulture programs scheduled for this week. Check back later for upcoming events.
                </p>
                <div class="mt-8">
                    <a href="<?php echo e(route('public.home')); ?>" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                        <svg class="mr-2 -ml-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Back to Home
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public-layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\_csb-mrma.in (2)\csb\resources\views/public/upcoming-programs.blade.php ENDPATH**/ ?>