<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            // VIP Participation fields
            $table->boolean('vip_participated')->default(false);
            $table->boolean('vip_mla_mp')->default(false);
            $table->boolean('vip_ms_jst_director')->default(false);
            $table->boolean('vip_local_pr')->default(false);
            $table->boolean('vip_state_official')->default(false);
            $table->boolean('vip_others')->default(false);
            $table->text('vip_others_details')->nullable();
            
            // Media Coverage fields
            $table->boolean('media_coverage')->default(false);
            $table->boolean('media_newspaper')->default(false);
            $table->boolean('media_tv_news')->default(false);
            $table->boolean('media_radio')->default(false);
            $table->string('media_link')->nullable(); // Single media link as text
            $table->string('media_photo')->nullable(); // Photo for media coverage

            // Social Media Post Link
            $table->string('social_media_link')->nullable(); // Single social media link as text
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            $table->dropColumn([
                'vip_participated',
                'vip_mla_mp',
                'vip_ms_jst_director',
                'vip_local_pr',
                'vip_state_official',
                'vip_others',
                'vip_others_details',
                'media_coverage',
                'media_newspaper',
                'media_tv_news',
                'media_radio',
                'media_link',
                'media_photo',
                'social_media_link'
            ]);
        });
    }
};
