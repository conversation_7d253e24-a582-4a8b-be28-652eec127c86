@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Feedback Analytics Dashboard') }}</h5>
                    <a href="{{ route('district-state-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Analyze participant feedback from events in your districts.</li>
                            <li>Monitor trends in feedback ratings and participant satisfaction.</li>
                            <li>Use insights to improve future event planning and execution.</li>
                        </ul>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <form id="filterForm" class="row g-3">
                                        <div class="col-md-4">
                                            <label for="startDate" class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="startDate" name="start_date">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="endDate" class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="endDate" name="end_date">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button type="submit" class="btn btn-primary">Apply Filter</button>
                                                <button type="button" class="btn btn-secondary" onclick="resetFilter()">Reset</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Events</h5>
                                    <h2 id="totalEvents">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Feedback</h5>
                                    <h2 id="totalFeedback">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Avg Benefit Rating</h5>
                                    <h2 id="avgBenefitRating">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <h5 class="card-title">Would Recommend</h5>
                                    <h2 id="wouldRecommendPercentage">-</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts and Analytics -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">District-wise Feedback Summary</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>District</th>
                                                    <th>Events</th>
                                                    <th>Feedback</th>
                                                    <th>Avg Rating</th>
                                                    <th>Recommend %</th>
                                                </tr>
                                            </thead>
                                            <tbody id="districtStatsBody">
                                                <!-- District stats will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Feedback Source Distribution</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <h4 id="webFeedback">-</h4>
                                            <small>Web Forms</small>
                                        </div>
                                        <div class="col-md-4">
                                            <h4 id="excelFeedback">-</h4>
                                            <small>Excel Import</small>
                                        </div>
                                        <div class="col-md-4">
                                            <h4 id="mobileFeedback">-</h4>
                                            <small>Mobile App</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trend Analysis -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Monthly Feedback Trends</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Month</th>
                                                    <th>Feedback Count</th>
                                                    <th>Avg Benefit Rating</th>
                                                    <th>Avg Speaker Rating</th>
                                                </tr>
                                            </thead>
                                            <tbody id="trendDataBody">
                                                <!-- Trend data will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rating Distribution -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Benefit Rating Distribution (1-10)</h6>
                                </div>
                                <div class="card-body">
                                    <div id="benefitRatingChart">
                                        <!-- Chart will be rendered here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Speaker Rating Distribution (1-5)</h6>
                                </div>
                                <div class="card-body">
                                    <div id="speakerRatingChart">
                                        <!-- Chart will be rendered here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Topic Analysis -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Popular Topics in Feedback</h6>
                                </div>
                                <div class="card-body">
                                    <div id="topicAnalysisBody">
                                        <!-- Topic analysis will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const filterForm = document.getElementById('filterForm');

        // Set default date range (last 3 months)
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 3);

        document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

        // Load initial data
        loadAnalyticsData();

        // Handle filter form submission
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loadAnalyticsData();
        });

        function loadAnalyticsData() {
            const formData = new FormData(filterForm);
            const params = new URLSearchParams(formData);

            fetch(`{{ route("district-state-coordinator.feedback-analytics.get-analytics-data") }}?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    updateSummaryStats(data.stats);
                    updateDistrictStats(data.district_stats);
                    updateTrendData(data.trend_data);
                    updateRatingDistribution(data.rating_distribution);
                    updateTopicAnalysis(data.topic_analysis);
                })
                .catch(error => {
                    console.error('Error loading analytics data:', error);
                    alert('Failed to load analytics data');
                });
        }

        function updateSummaryStats(stats) {
            document.getElementById('totalEvents').textContent = stats.total_events || 0;
            document.getElementById('totalFeedback').textContent = stats.total_feedback || 0;
            document.getElementById('avgBenefitRating').textContent = stats.avg_benefit_rating || '0.0';
            document.getElementById('wouldRecommendPercentage').textContent = (stats.would_recommend_percentage || 0) + '%';

            // Update source distribution
            document.getElementById('webFeedback').textContent = stats.by_source.web || 0;
            document.getElementById('excelFeedback').textContent = stats.by_source.excel || 0;
            document.getElementById('mobileFeedback').textContent = stats.by_source.mobile || 0;
        }

        function updateDistrictStats(districtStats) {
            const tbody = document.getElementById('districtStatsBody');
            tbody.innerHTML = '';

            if (districtStats && districtStats.length > 0) {
                districtStats.forEach(district => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${district.district_name}</td>
                        <td>${district.events_count}</td>
                        <td>${district.feedback_count}</td>
                        <td>${district.avg_benefit_rating}</td>
                        <td>${district.would_recommend_percentage}%</td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center">No district data available</td></tr>';
            }
        }

        function updateTrendData(trendData) {
            const tbody = document.getElementById('trendDataBody');
            tbody.innerHTML = '';

            if (trendData && trendData.length > 0) {
                trendData.forEach(trend => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${trend.month_name}</td>
                        <td>${trend.feedback_count}</td>
                        <td>${trend.avg_benefit_rating}</td>
                        <td>${trend.avg_speaker_rating}</td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center">No trend data available</td></tr>';
            }
        }

        function updateRatingDistribution(ratingDistribution) {
            // Simple text-based chart for benefit ratings
            const benefitChart = document.getElementById('benefitRatingChart');
            let benefitHtml = '';
            
            if (ratingDistribution && ratingDistribution.benefit) {
                ratingDistribution.benefit.forEach(item => {
                    const barWidth = Math.max(item.percentage, 5); // Minimum 5% width for visibility
                    benefitHtml += `
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Rating ${item.rating}</span>
                                <span>${item.count} (${item.percentage}%)</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${barWidth}%"></div>
                            </div>
                        </div>
                    `;
                });
            } else {
                benefitHtml = '<p class="text-muted">No rating data available</p>';
            }
            benefitChart.innerHTML = benefitHtml;

            // Simple text-based chart for speaker ratings
            const speakerChart = document.getElementById('speakerRatingChart');
            let speakerHtml = '';
            
            if (ratingDistribution && ratingDistribution.speaker) {
                ratingDistribution.speaker.forEach(item => {
                    const barWidth = Math.max(item.percentage, 5); // Minimum 5% width for visibility
                    speakerHtml += `
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Rating ${item.rating}</span>
                                <span>${item.count} (${item.percentage}%)</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: ${barWidth}%"></div>
                            </div>
                        </div>
                    `;
                });
            } else {
                speakerHtml = '<p class="text-muted">No rating data available</p>';
            }
            speakerChart.innerHTML = speakerHtml;
        }

        function updateTopicAnalysis(topicAnalysis) {
            const container = document.getElementById('topicAnalysisBody');
            
            if (topicAnalysis && topicAnalysis.length > 0) {
                let html = '<div class="row">';
                topicAnalysis.forEach(topic => {
                    html += `
                        <div class="col-md-3 mb-2">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5>${topic.mentions}</h5>
                                    <small>${topic.topic}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-muted">No topic analysis data available</p>';
            }
        }

        window.resetFilter = function() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 3);

            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            
            loadAnalyticsData();
        };
    });
</script>
@endpush
