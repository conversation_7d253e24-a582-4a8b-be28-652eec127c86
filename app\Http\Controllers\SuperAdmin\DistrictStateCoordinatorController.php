<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\DistrictStateCoordinator;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DistrictStateCoordinatorController extends Controller
{
    /**
     * Display the district state coordinator management page.
     */
    public function index()
    {
        return view('super-admin.district-state-coordinator.index');
    }

    /**
     * Get all district state coordinators.
     */
    public function getAll()
    {
        try {
            $coordinators = DistrictStateCoordinator::all();

            // Get district assignments for each coordinator
            foreach ($coordinators as $coordinator) {
                $districts = $coordinator->districts()->select('state_data.id', 'state_data.state', 'state_data.district', 'state_data.status')->get();
                $coordinator->districts = $districts;
            }

            return response()->json($coordinators);
        } catch (\Exception $e) {
            Log::error('Error getting district state coordinators: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get district state coordinators'], 500);
        }
    }

    /**
     * Get coordinator details.
     */
    public function getDetails($id)
    {
        try {
            // Try to find the coordinator, but don't throw an exception if not found
            $coordinator = DistrictStateCoordinator::find($id);

            if (!$coordinator) {
                Log::warning('Attempted to get details for non-existent District State Coordinator with ID: ' . $id);
                return response()->json(['error' => 'District State Coordinator not found'], 404);
            }

            // Get district assignments
            $districts = $coordinator->districts()->select('state_data.id', 'state_data.state', 'state_data.district', 'state_data.status')->get();
            $coordinator->districts = $districts;

            return response()->json($coordinator);
        } catch (\Exception $e) {
            Log::error('Error getting coordinator details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get coordinator details'], 500);
        }
    }

    /**
     * Get available districts.
     */
    public function getAvailableDistricts()
    {
        try {
            $districts = StateData::select('id', 'state', 'district', 'status')
                ->orderBy('state')
                ->orderBy('district')
                ->get();

            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error getting available districts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get available districts'], 500);
        }
    }

    /**
     * Create or update a district state coordinator.
     */
    public function save(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'designation' => 'required|string|max:100',
            'expertise_areas' => 'required|array',
            'role' => 'required|string',
            'district_ids' => 'required|array',
            'password' => 'required_if:id,|nullable|string|min:4',
        ]);

        // Start a transaction
        DB::beginTransaction();

        try {
            // Ensure designation and role are properly quoted strings
            $designation = is_string($request->designation) ? $request->designation : (string)$request->designation;
            $role = is_string($request->role) ? $request->role : (string)$request->role;

            $user = null;

            // If ID is provided, find user by ID (for updates)
            if ($request->filled('id')) {
                $coordinator = DistrictStateCoordinator::find($request->id);
                if ($coordinator) {
                    $user = $coordinator->user;

                    // Check if email is being changed to an existing email
                    if ($user && $user->email !== $request->email) {
                        $existingEmailUser = \App\Models\User::where('email', $request->email)
                            ->where('id', '!=', $user->id)
                            ->first();

                        if ($existingEmailUser) {
                            return response()->json(['error' => 'Email already exists for another user'], 422);
                        }
                    }
                }
            } else {
                // For new users, check if email already exists
                $existingEmailUser = \App\Models\User::where('email', $request->email)->first();
                if ($existingEmailUser) {
                    return response()->json(['error' => 'Email already exists'], 422);
                }
            }

            if ($user) {
                // Update existing user
                $user->name = $request->name;
                $user->email = $request->email;
                $user->phone_number = $request->phone_number;
                $user->designation = $designation;
                $user->role = 'district_state_coordinator';

                // Update password if provided
                if ($request->filled('password')) {
                    $user->password = \Illuminate\Support\Facades\Hash::make($request->password);
                }

                $user->save();
                Log::info('Updated existing user for District State Coordinator: ' . $user->id);
            } else {
                // Create new user
                if (!$request->filled('password')) {
                    throw new \Exception('Password is required when creating a new District State Coordinator');
                }

                $user = \App\Models\User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'designation' => $designation,
                    'password' => \Illuminate\Support\Facades\Hash::make($request->password),
                    'role' => 'district_state_coordinator',
                ]);
                Log::info('Created new user for District State Coordinator: ' . $user->id);
            }

            if ($request->has('id') && !empty($request->id)) {
                // Try to find the coordinator, but don't throw an exception if not found
                $coordinator = DistrictStateCoordinator::find($request->id);

                if ($coordinator) {
                    // Update existing coordinator
                    $coordinator->update([
                        'name' => $request->name,
                        'email' => $request->email,
                        'phone_number' => $request->phone_number,
                        'designation' => $designation,
                        'expertise_areas' => $request->expertise_areas,
                        'role' => $role,
                        'user_id' => $user->id,
                    ]);
                    Log::info('Updated existing District State Coordinator: ' . $coordinator->id);
                } else {
                    // If coordinator with the given ID doesn't exist, create a new one
                    Log::warning('District State Coordinator with ID ' . $request->id . ' not found. Creating a new record instead.');
                    $coordinator = DistrictStateCoordinator::create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'phone_number' => $request->phone_number,
                        'designation' => $designation,
                        'expertise_areas' => $request->expertise_areas,
                        'role' => $role,
                        'user_id' => $user->id,
                    ]);
                    Log::info('Created new District State Coordinator: ' . $coordinator->id);
                }
            } else {
                // Create a new coordinator
                $coordinator = DistrictStateCoordinator::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'designation' => $designation,
                    'expertise_areas' => $request->expertise_areas,
                    'role' => $role,
                    'user_id' => $user->id,
                ]);
                Log::info('Created new District State Coordinator: ' . $coordinator->id);
            }

            // Update district assignments
            $coordinator->districts()->detach();
            $coordinator->districts()->attach($request->district_ids);

            DB::commit();
            return response()->json(['message' => 'District State Coordinator saved successfully', 'id' => $coordinator->id]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving district state coordinator: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save district state coordinator: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a district state coordinator.
     */
    public function delete($id)
    {
        // Start a transaction for data integrity
        DB::beginTransaction();

        try {
            // Try to find the coordinator, but don't throw an exception if not found
            $coordinator = DistrictStateCoordinator::find($id);

            if (!$coordinator) {
                // If coordinator doesn't exist, return success message anyway
                Log::warning('Attempted to delete non-existent District State Coordinator with ID: ' . $id);
                DB::rollBack();
                return response()->json(['message' => 'District State Coordinator deleted successfully']);
            }

            // Get the associated user before deleting the coordinator
            $user = $coordinator->user;

            // Delete district assignments
            $coordinator->districts()->detach();
            Log::info('Detached districts for District State Coordinator: ' . $coordinator->id);

            // Delete the coordinator record
            $coordinator->delete();
            Log::info('Deleted District State Coordinator record: ' . $id);

            // Delete the associated user record if it exists
            if ($user) {
                $user->delete();
                Log::info('Deleted associated User record: ' . $user->id . ' (email: ' . $user->email . ')');
            } else {
                Log::warning('No associated user found for District State Coordinator: ' . $id);
            }

            DB::commit();
            return response()->json(['message' => 'District State Coordinator and associated user deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting district state coordinator: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete district state coordinator: ' . $e->getMessage()], 500);
        }
    }
}
