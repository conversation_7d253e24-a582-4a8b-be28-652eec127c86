<?php $__env->startSection('title', 'Pending Participation Requests'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Pending Participation Requests</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($setupMessage)): ?>
                        <div class="alert alert-warning">
                            <p><strong>Profile Setup in Progress</strong></p>
                            <p><?php echo e($setupMessage); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p><strong>Instructions:</strong></p>
                            <ul>
                                <li>Review participation requests from scientists for action plan activities.</li>
                                <li>Accept requests if you are available and have the expertise to participate.</li>
                                <li>Reject requests if you are unavailable or the activity is outside your expertise area.</li>
                            </ul>
                        </div>

                        <div class="table-responsive">
                        <table class="table table-striped" id="requestsTable">
                            <thead>
                                <tr>
                                    <th>Scientist</th>
                                    <th>Action Plan</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>Message</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="requestsTableBody">
                                <!-- Requests will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div id="noRequests" class="alert alert-info d-none">
                        No pending participation requests found.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="responseModalLabel">Respond to Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="responseForm">
                    <input type="hidden" id="requestId" name="requestId">
                    <input type="hidden" id="responseType" name="responseType">

                    <div class="mb-3">
                        <label for="responseMessage" class="form-label">Response Message:</label>
                        <textarea class="form-control" id="responseMessage" name="responseMessage" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitResponseBtn">Submit Response</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const requestsTableBody = document.getElementById('requestsTableBody');
        const noRequests = document.getElementById('noRequests');
        const responseModal = new bootstrap.Modal(document.getElementById('responseModal'));
        const responseForm = document.getElementById('responseForm');
        const requestId = document.getElementById('requestId');
        const responseType = document.getElementById('responseType');
        const responseMessage = document.getElementById('responseMessage');
        const submitResponseBtn = document.getElementById('submitResponseBtn');

        // Load pending requests
        loadPendingRequests();

        function loadPendingRequests() {
            fetch('<?php echo e(route("district-state-coordinator.requests.pending-requests")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }

                    if (data.length === 0) {
                        requestsTableBody.innerHTML = '';
                        noRequests.classList.remove('d-none');
                        return;
                    }

                    noRequests.classList.add('d-none');
                    requestsTableBody.innerHTML = '';

                    data.forEach(request => {
                        const row = document.createElement('tr');

                        // Format date
                        const date = new Date(request.action_plan.planned_date);
                        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        row.innerHTML = `
                            <td>${request.scientist.name}<br><small>${request.scientist.email}</small></td>
                            <td>${request.action_plan.title}</td>
                            <td>${request.action_plan.type.replace('_', ' ').toUpperCase()}</td>
                            <td>${formattedDate}</td>
                            <td>${request.action_plan.location}</td>
                            <td>${request.request_message}</td>
                            <td>
                                <button class="btn btn-success btn-sm accept-btn" data-id="${request.id}">Accept</button>
                                <button class="btn btn-danger btn-sm reject-btn" data-id="${request.id}">Reject</button>
                            </td>
                        `;

                        requestsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while loading requests');
                });
        }

        // Accept button click
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('accept-btn')) {
                const id = e.target.getAttribute('data-id');
                requestId.value = id;
                responseType.value = 'accept';
                responseMessage.value = 'I am available to participate in your action plan activity.';
                document.getElementById('responseModalLabel').textContent = 'Accept Request';
                responseModal.show();
            }
        });

        // Reject button click
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('reject-btn')) {
                const id = e.target.getAttribute('data-id');
                requestId.value = id;
                responseType.value = 'reject';
                responseMessage.value = '';
                document.getElementById('responseModalLabel').textContent = 'Reject Request';
                responseModal.show();
            }
        });

        // Submit response
        submitResponseBtn.addEventListener('click', function() {
            if (!responseMessage.value.trim()) {
                alert('Please enter a response message');
                return;
            }

            const id = requestId.value;
            const type = responseType.value;
            const message = responseMessage.value;

            let url = '';
            if (type === 'accept') {
                url = `<?php echo e(url('district-state-coordinator/requests/accept')); ?>/${id}`;
            } else {
                url = `<?php echo e(url('district-state-coordinator/requests/reject')); ?>/${id}`;
            }

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({
                    response_message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    responseModal.hide();
                    loadPendingRequests();
                } else if (data.error) {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while submitting response');
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/requests/index.blade.php ENDPATH**/ ?>