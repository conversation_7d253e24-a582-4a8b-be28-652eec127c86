<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ZonalCoordinator;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ZonalCoordinatorController extends Controller
{
    /**
     * Display the zonal coordinator management page.
     */
    public function index()
    {
        return view('super-admin.zonal-coordinator.index');
    }

    /**
     * Get all zonal coordinators.
     */
    public function getCoordinators()
    {
        try {
            $coordinators = ZonalCoordinator::with('user')->get();

            // Get district assignments for each coordinator
            foreach ($coordinators as $coordinator) {
                $districts = DB::table('district_zonal_coordinator')
                    ->where('zonal_coordinator_id', $coordinator->id)
                    ->join('state_data', 'district_zonal_coordinator.district_id', '=', 'state_data.id')
                    ->select('state_data.id', 'state_data.state', 'state_data.district', 'state_data.status')
                    ->get();

                $coordinator->districts = $districts;
            }

            return response()->json($coordinators);
        } catch (\Exception $e) {
            Log::error('Error getting zonal coordinators: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get zonal coordinators'], 500);
        }
    }

    /**
     * Get coordinator details.
     */
    public function getCoordinatorDetails($id)
    {
        try {
            // Try to find the coordinator, but don't throw an exception if not found
            $coordinator = ZonalCoordinator::with('user')->find($id);

            if (!$coordinator) {
                Log::warning('Attempted to get details for non-existent Zonal Coordinator with ID: ' . $id);
                return response()->json(['error' => 'Zonal Coordinator not found'], 404);
            }

            // Get district assignments
            $districts = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->join('state_data', 'district_zonal_coordinator.district_id', '=', 'state_data.id')
                ->select('state_data.id', 'state_data.state', 'state_data.district', 'state_data.status')
                ->get();

            $coordinator->districts = $districts;

            return response()->json($coordinator);
        } catch (\Exception $e) {
            Log::error('Error getting coordinator details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get coordinator details'], 500);
        }
    }

    /**
     * Create or update a zonal coordinator.
     */
    public function saveCoordinator(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'designation' => 'required|string|max:100',
            'zone_name' => 'required|string|max:100',
            'district_ids' => 'required|array',
            'password' => 'required_if:is_new,true|nullable|string|min:4',
        ]);

        // Verify all districts exist (removed same-state restriction)
        $districts = StateData::whereIn('id', $request->district_ids)->get();

        if ($districts->count() !== count($request->district_ids)) {
            return response()->json(['error' => 'One or more selected districts do not exist'], 422);
        }

        // Start a transaction
        DB::beginTransaction();

        try {
            $user = null;

            // If ID is provided, find user by ID (for updates)
            if ($request->filled('id')) {
                $coordinator = ZonalCoordinator::find($request->id);
                if ($coordinator) {
                    $user = $coordinator->user;

                    // Check if email is being changed to an existing email
                    if ($user && $user->email !== $request->email) {
                        $existingEmailUser = User::where('email', $request->email)
                            ->where('id', '!=', $user->id)
                            ->first();

                        if ($existingEmailUser) {
                            return response()->json(['error' => 'Email already exists for another user'], 422);
                        }
                    }
                }
            } else {
                // For new users, check if email already exists
                $existingEmailUser = User::where('email', $request->email)->first();
                if ($existingEmailUser) {
                    return response()->json(['error' => 'Email already exists'], 422);
                }
            }

            if ($user) {
                // Update existing user
                $user->name = $request->name;
                $user->email = $request->email;
                $user->phone_number = $request->phone_number;
                $user->designation = $request->designation;
                $user->role = 'zonal_coordinator';

                // Update password if provided
                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                }

                $user->save();
            } else {
                // Create new user
                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'designation' => $request->designation,
                    'password' => Hash::make($request->password),
                    'role' => 'zonal_coordinator',
                ]);
            }

            // Find or create the zonal coordinator
            if ($request->has('id') && !empty($request->id)) {
                // Try to find the coordinator, but don't throw an exception if not found
                $coordinator = ZonalCoordinator::find($request->id);

                if ($coordinator) {
                    // Update existing coordinator
                    $coordinator->user_id = $user->id;
                    $coordinator->zone_name = $request->zone_name;
                    $coordinator->save();
                } else {
                    // If coordinator with the given ID doesn't exist, create a new one
                    Log::warning('Zonal Coordinator with ID ' . $request->id . ' not found. Creating a new record instead.');
                    $coordinator = ZonalCoordinator::create([
                        'user_id' => $user->id,
                        'zone_name' => $request->zone_name,
                    ]);
                }
            } else {
                // Create a new coordinator
                $coordinator = ZonalCoordinator::create([
                    'user_id' => $user->id,
                    'zone_name' => $request->zone_name,
                ]);
            }

            // Update district assignments
            DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->delete();

            foreach ($request->district_ids as $districtId) {
                DB::table('district_zonal_coordinator')->insert([
                    'zonal_coordinator_id' => $coordinator->id,
                    'district_id' => $districtId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Commit the transaction
            DB::commit();

            return response()->json([
                'message' => 'Zonal coordinator saved successfully',
                'id' => $coordinator->id
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            Log::error('Error saving zonal coordinator: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save zonal coordinator: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a zonal coordinator.
     */
    public function deleteCoordinator($id)
    {
        // Start a transaction
        DB::beginTransaction();

        try {
            // Try to find the coordinator, but don't throw an exception if not found
            $coordinator = ZonalCoordinator::find($id);

            if (!$coordinator) {
                // If coordinator doesn't exist, return success message anyway
                Log::warning('Attempted to delete non-existent Zonal Coordinator with ID: ' . $id);
                DB::commit();
                return response()->json(['message' => 'Zonal coordinator deleted successfully']);
            }

            // Delete district assignments
            DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->delete();

            // Delete the coordinator
            $coordinator->delete();

            // Commit the transaction
            DB::commit();

            return response()->json(['message' => 'Zonal coordinator deleted successfully']);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            Log::error('Error deleting zonal coordinator: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete zonal coordinator'], 500);
        }
    }

    /**
     * Get all available districts.
     */
    public function getAvailableDistricts()
    {
        try {
            $districts = StateData::select('id', 'state', 'district', 'status')
                ->orderBy('state')
                ->orderBy('district')
                ->get();

            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error getting available districts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get available districts'], 500);
        }
    }
}
