<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\User;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EventManagementController extends Controller
{
    /**
     * Display the event management page.
     */
    public function index()
    {
        return view('super-admin.event-management.index');
    }

    /**
     * Get all events.
     */
    public function getEvents()
    {
        try {
            // Get action plans and map them to the expected event structure
            $actionPlans = Event::with(['scientist:id,name,email', 'district:id,state,district'])
                ->orderBy('planned_date', 'desc')
                ->get();

            // Transform action plans to match the expected event structure
            $events = $actionPlans->map(function ($plan) {
                // Get participant data from actionplan_feedback table if available
                $feedback = \App\Models\ActionPlanFeedback::where('action_plan_id', $plan->id)->first();

                if ($feedback) {
                    // Use data from feedback table (this is the actual submitted data)
                    $genderBasedCount = ($feedback->male_participants ?? 0) +
                                       ($feedback->female_participants ?? 0) +
                                       ($feedback->transgender_participants ?? 0);

                    $casteBasedCount = ($feedback->st_participants ?? 0) +
                                      ($feedback->sc_participants ?? 0) +
                                      ($feedback->general_participants ?? 0) +
                                      ($feedback->obc_participants ?? 0);

                    $actualParticipants = max($genderBasedCount, $casteBasedCount);

                    $demographics = [
                        'male' => $feedback->male_participants ?? 0,
                        'female' => $feedback->female_participants ?? 0,
                        'transgender' => $feedback->transgender_participants ?? 0,
                        'st' => $feedback->st_participants ?? 0,
                        'sc' => $feedback->sc_participants ?? 0,
                        'general' => $feedback->general_participants ?? 0,
                        'obc' => $feedback->obc_participants ?? 0
                    ];
                } else {
                    // Fallback to action plan table data (legacy or incomplete data)
                    $genderBasedCount = ($plan->male_participants ?? 0) +
                                       ($plan->female_participants ?? 0) +
                                       ($plan->transgender_participants ?? 0);

                    $casteBasedCount = ($plan->st_participants ?? 0) +
                                      ($plan->sc_participants ?? 0) +
                                      ($plan->general_participants ?? 0) +
                                      ($plan->obc_participants ?? 0);

                    $actualParticipants = max($genderBasedCount, $casteBasedCount);

                    $demographics = [
                        'male' => $plan->male_participants ?? 0,
                        'female' => $plan->female_participants ?? 0,
                        'transgender' => $plan->transgender_participants ?? 0,
                        'st' => $plan->st_participants ?? 0,
                        'sc' => $plan->sc_participants ?? 0,
                        'general' => $plan->general_participants ?? 0,
                        'obc' => $plan->obc_participants ?? 0
                    ];
                }

                // Convert to array and add event-specific fields
                $eventData = $plan->toArray();
                $eventData['start_date'] = $plan->planned_date ? $plan->planned_date->toDateTimeString() : null;
                $eventData['end_date'] = $plan->planned_date ? $plan->planned_date->toDateTimeString() : null;
                $eventData['actual_participants'] = $actualParticipants;
                $eventData['participant_demographics'] = $demographics;
                $eventData['topic'] = $plan->type;
                $eventData['description'] = "Action Plan ID: {$plan->id}, Type: {$plan->type}";

                return $eventData;
            });

            return response()->json($events);
        } catch (\Exception $e) {
            Log::error('Error getting events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get events: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get event details.
     */
    public function getEventDetails($id)
    {
        try {
            $plan = Event::with(['scientist:id,name,email', 'district:id,state,district'])
                ->findOrFail($id);

            // Get feedback data from actionplan_feedback table if available
            $feedback = \App\Models\ActionPlanFeedback::where('action_plan_id', $id)->first();

            if ($feedback) {
                // Override action plan participant data with feedback data (actual submitted data)
                $plan->male_participants = $feedback->male_participants;
                $plan->female_participants = $feedback->female_participants;
                $plan->transgender_participants = $feedback->transgender_participants;
                $plan->st_participants = $feedback->st_participants;
                $plan->sc_participants = $feedback->sc_participants;
                $plan->general_participants = $feedback->general_participants;
                $plan->obc_participants = $feedback->obc_participants;
                $plan->photo1 = $feedback->photo1;
                $plan->photo2 = $feedback->photo2;
                $plan->photo3 = $feedback->photo3;
                $plan->photo4 = $feedback->photo4;
                $plan->gis_location = $feedback->gis_location;
                $plan->scientist_event_success_rating = $feedback->event_success_rating;
                $plan->scientist_challenges_faced = $feedback->challenges_faced;
                $plan->scientist_suggestions_for_improvement = $feedback->suggestions_for_improvement;
                $plan->scientist_objectives_met = $feedback->objectives_met;
                $plan->scientist_self_assessed_learning_outcome = $feedback->self_assessed_learning_outcome;
            }

            // Calculate actual participants from demographic data
            // Method 1: Use gender-based demographics
            $genderBasedCount = ($plan->male_participants ?? 0) +
                               ($plan->female_participants ?? 0) +
                               ($plan->transgender_participants ?? 0);

            // Method 2: Use caste-based demographics
            $casteBasedCount = ($plan->st_participants ?? 0) +
                              ($plan->sc_participants ?? 0) +
                              ($plan->general_participants ?? 0) +
                              ($plan->obc_participants ?? 0);

            // Use the larger of the two counts to ensure we don't undercount
            $actualParticipants = max($genderBasedCount, $casteBasedCount);

            // Create participant demographics JSON if needed
            $demographics = [
                'male' => $plan->male_participants ?? 0,
                'female' => $plan->female_participants ?? 0,
                'transgender' => $plan->transgender_participants ?? 0,
                'st' => $plan->st_participants ?? 0,
                'sc' => $plan->sc_participants ?? 0,
                'general' => $plan->general_participants ?? 0,
                'obc' => $plan->obc_participants ?? 0
            ];

            // Convert to array and add event-specific fields
            $eventData = $plan->toArray();
            $eventData['start_date'] = $plan->planned_date ? $plan->planned_date->toDateTimeString() : null;
            $eventData['end_date'] = $plan->planned_date ? $plan->planned_date->toDateTimeString() : null;
            $eventData['actual_participants'] = $actualParticipants;
            $eventData['participant_demographics'] = $demographics;
            $eventData['topic'] = $plan->type;
            $eventData['description'] = "Action Plan ID: {$plan->id}, Type: {$plan->type}";

            return response()->json($eventData);
        } catch (\Exception $e) {
            Log::error('Error getting event details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get event details: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Create or update an event.
     */
    public function saveEvent(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location' => 'required|string|max:255',
            'district_id' => 'required|exists:state_data,id',
            'scientist_id' => 'required|exists:users,id',
            'expected_participants' => 'required|integer|min:1',
            'actual_participants' => 'nullable|integer|min:0',
            'participant_demographics' => 'nullable|array',
            'topic' => 'required|string|max:255',
            'status' => 'required|string|in:planned,completed,cancelled',
        ]);

        try {
            // Ensure the scientist exists and has the correct role
            User::where('id', $request->scientist_id)
                ->where('role', 'scientist')
                ->firstOrFail();

            // Ensure the district exists
            StateData::findOrFail($request->district_id);

            // Extract demographic data from participant_demographics if provided
            $maleParticipants = 0;
            $femaleParticipants = 0;
            $transgenderParticipants = 0;
            $stParticipants = 0;
            $scParticipants = 0;
            $generalParticipants = 0;
            $obcParticipants = 0;

            // If actual_participants is provided but no demographics, we need to ensure the counts add up
            $actualParticipants = $request->actual_participants ? (int)$request->actual_participants : 0;

            if ($request->has('participant_demographics') && is_array($request->participant_demographics)) {
                $demographics = $request->participant_demographics;
                $maleParticipants = $demographics['male'] ?? 0;
                $femaleParticipants = $demographics['female'] ?? 0;
                $transgenderParticipants = $demographics['transgender'] ?? 0;
                $stParticipants = $demographics['st'] ?? 0;
                $scParticipants = $demographics['sc'] ?? 0;
                $generalParticipants = $demographics['general'] ?? 0;
                $obcParticipants = $demographics['obc'] ?? 0;

                // Calculate gender and caste totals
                $genderTotal = $maleParticipants + $femaleParticipants + $transgenderParticipants;
                $casteTotal = $stParticipants + $scParticipants + $generalParticipants + $obcParticipants;

                // If there's a mismatch between actual_participants and demographic totals,
                // adjust the demographics to match actual_participants
                if ($actualParticipants > 0 && $genderTotal != $actualParticipants && $casteTotal != $actualParticipants) {
                    // If gender total is 0, distribute evenly
                    if ($genderTotal == 0 && $actualParticipants > 0) {
                        $maleParticipants = floor($actualParticipants / 2);
                        $femaleParticipants = $actualParticipants - $maleParticipants;
                    }

                    // If caste total is 0, distribute evenly
                    if ($casteTotal == 0 && $actualParticipants > 0) {
                        $generalParticipants = $actualParticipants;
                    }
                }
            } else if ($actualParticipants > 0) {
                // If no demographics provided but actual_participants is set, create default demographics
                $maleParticipants = floor($actualParticipants / 2);
                $femaleParticipants = $actualParticipants - $maleParticipants;
                $generalParticipants = $actualParticipants;
            }

            // Map event fields to action plan fields
            $actionPlanData = [
                'title' => $request->title,
                'location' => $request->location,
                'planned_date' => $request->start_date,
                'district_id' => $request->district_id,
                'scientist_id' => $request->scientist_id,
                'expected_participants' => $request->expected_participants,
                'type' => $request->topic,
                'status' => $request->status,
                'male_participants' => $maleParticipants,
                'female_participants' => $femaleParticipants,
                'transgender_participants' => $transgenderParticipants,
                'st_participants' => $stParticipants,
                'sc_participants' => $scParticipants,
                'general_participants' => $generalParticipants,
                'obc_participants' => $obcParticipants,
            ];

            if ($request->has('id')) {
                $actionPlan = Event::findOrFail($request->id);
                $actionPlan->update($actionPlanData);

                return response()->json(['message' => 'Event updated successfully', 'id' => $actionPlan->id]);
            } else {
                $actionPlan = Event::create($actionPlanData);

                return response()->json(['message' => 'Event created successfully', 'id' => $actionPlan->id]);
            }
        } catch (\Exception $e) {
            Log::error('Error saving event: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save event: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete an event.
     */
    public function deleteEvent($id)
    {
        try {
            $event = Event::findOrFail($id);
            $event->delete();

            return response()->json(['message' => 'Event deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting event: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete event'], 500);
        }
    }

    /**
     * Get all scientists for dropdown.
     */
    public function getScientists()
    {
        try {
            $scientists = User::where('role', 'scientist')
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();

            return response()->json($scientists);
        } catch (\Exception $e) {
            Log::error('Error getting scientists: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientists'], 500);
        }
    }

    /**
     * Get all districts for dropdown.
     */
    public function getDistricts()
    {
        try {
            $districts = StateData::select('id', 'state', 'district', 'status')
                ->orderBy('state')
                ->orderBy('district')
                ->get();

            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error getting districts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get districts'], 500);
        }
    }
}
