<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Events in My Districts')); ?></h5>
                    <a href="<?php echo e(route('district-state-coordinator.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all events/action plans conducted by scientists in your districts.</li>
                            <li>Monitor event progress and participant engagement.</li>
                            <li>Click on an event to view detailed information.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Scientist</th>
                                    <th>District</th>
                                    <th>Date</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- Events will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Event Details Modal -->
                    <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Event Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Title</th>
                                                    <td id="eventTitle"></td>
                                                </tr>
                                                <tr>
                                                    <th>Type</th>
                                                    <td id="eventType"></td>
                                                </tr>
                                                <tr>
                                                    <th>Location</th>
                                                    <td id="eventLocation"></td>
                                                </tr>
                                                <tr>
                                                    <th>Date</th>
                                                    <td id="eventDate"></td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td id="eventStatus"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Participants & Performance</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Expected Participants</th>
                                                    <td id="expectedParticipants"></td>
                                                </tr>
                                                <tr>
                                                    <th>Actual Participants</th>
                                                    <td id="actualParticipants"></td>
                                                </tr>
                                                <tr>
                                                    <th>Scientist</th>
                                                    <td id="eventScientist"></td>
                                                </tr>
                                                <tr>
                                                    <th>District</th>
                                                    <td id="eventDistrict"></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="feedbackSection" style="display: none;">
                                        <hr>
                                        <h6>Coordinator Feedback</h6>
                                        <div id="feedbackContent"></div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const eventsTableBody = document.getElementById('eventsTableBody');
        const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));

        // Load events
        loadEvents();

        // Load events function
        function loadEvents() {
            fetch('<?php echo e(route("district-state-coordinator.events.get-events")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    eventsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        eventsTableBody.innerHTML = '<tr><td colspan="8" class="text-center">No events found in your districts.</td></tr>';
                        return;
                    }

                    data.forEach(event => {
                        // Handle date formatting with proper validation
                        let formattedDate = 'N/A';
                        if (event.start_date) {
                            const eventDate = new Date(event.start_date);
                            if (!isNaN(eventDate.getTime())) {
                                formattedDate = eventDate.toLocaleDateString();
                            }
                        }

                        const statusBadge = getStatusBadge(event.status);
                        const participantsText = `${event.actual_participants || 0}/${event.expected_participants || 0}`;

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${event.title}</td>
                            <td>${event.type || 'N/A'}</td>
                            <td>${event.scientist_name}</td>
                            <td>${event.state} - ${event.district}</td>
                            <td>${formattedDate}</td>
                            <td>${participantsText}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="viewEvent(${event.id})">View Details</button>
                            </td>
                        `;
                        eventsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading events:', error);
                    alert('Failed to load events');
                });
        }

        // Get status badge
        function getStatusBadge(status) {
            switch(status) {
                case 'completed':
                    return '<span class="badge bg-success">Completed</span>';
                case 'planned':
                    return '<span class="badge bg-primary">Planned</span>';
                case 'cancelled':
                    return '<span class="badge bg-danger">Cancelled</span>';
                default:
                    return '<span class="badge bg-secondary">Unknown</span>';
            }
        }

        // View event function
        window.viewEvent = function(id) {
            fetch(`<?php echo e(route("district-state-coordinator.events.event", ["id" => ":id"])); ?>`.replace(':id', id))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Populate modal with event data
                    document.getElementById('eventTitle').textContent = data.title;
                    document.getElementById('eventType').textContent = data.type || 'N/A';
                    document.getElementById('eventLocation').textContent = data.location || 'N/A';

                    // Handle date formatting with proper validation
                    let formattedDateTime = 'N/A';
                    if (data.start_date) {
                        const eventDate = new Date(data.start_date);
                        if (!isNaN(eventDate.getTime())) {
                            formattedDateTime = eventDate.toLocaleDateString() + ' ' + eventDate.toLocaleTimeString();
                        }
                    }
                    document.getElementById('eventDate').textContent = formattedDateTime;

                    document.getElementById('eventStatus').innerHTML = getStatusBadge(data.status);
                    document.getElementById('expectedParticipants').textContent = data.expected_participants || 0;
                    document.getElementById('actualParticipants').textContent = data.actual_participants || 0;
                    document.getElementById('eventScientist').textContent = data.scientist ? data.scientist.name : 'N/A';
                    document.getElementById('eventDistrict').textContent = data.district ? `${data.district.state} - ${data.district.district}` : 'N/A';

                    eventModal.show();
                })
                .catch(error => {
                    console.error('Error loading event details:', error);
                    alert('Failed to load event details');
                });
        };
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/events/index.blade.php ENDPATH**/ ?>