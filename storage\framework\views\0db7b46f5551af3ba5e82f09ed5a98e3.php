<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('My Districts')); ?></h5>
                    <a href="<?php echo e(route('zonal-coordinator.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all districts assigned to you as a Zonal Coordinator.</li>
                            <li>Click on a district to view its details and assigned scientist.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>District</th>
                                    <th>State</th>
                                    <th>Type</th>
                                    <th>Assigned Scientist</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="districtsTableBody">
                                <!-- Districts will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- District Details Modal -->
                    <div class="modal fade" id="districtModal" tabindex="-1" aria-labelledby="districtModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="districtModalLabel">District Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>District Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>District Name</th>
                                                    <td id="districtName"></td>
                                                </tr>
                                                <tr>
                                                    <th>State</th>
                                                    <td id="stateName"></td>
                                                </tr>
                                                <tr>
                                                    <th>Type</th>
                                                    <td id="districtType"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Assigned Scientist</h6>
                                            <div id="scientistInfo">
                                                <!-- Scientist info will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const districtsTableBody = document.getElementById('districtsTableBody');
        const districtModal = new bootstrap.Modal(document.getElementById('districtModal'));

        // Load districts
        loadDistricts();

        // Load districts function
        function loadDistricts() {
            fetch('<?php echo e(route("zonal-coordinator.districts.get-districts")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }
                    
                    districtsTableBody.innerHTML = '';
                    
                    data.forEach(district => {
                        const row = document.createElement('tr');
                        
                        // Format district type badge
                        const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                        const typeBadge = `<span class="badge ${badgeClass}">${district.status || 'Not specified'}</span>`;
                        
                        row.innerHTML = `
                            <td>${district.district}</td>
                            <td>${district.state}</td>
                            <td>${typeBadge}</td>
                            <td>${district.scientist || 'No scientist assigned'}</td>
                            <td>
                                <button class="btn btn-sm btn-primary view-btn" data-id="${district.id}">View Details</button>
                            </td>
                        `;
                        
                        districtsTableBody.appendChild(row);
                    });
                    
                    // Add event listeners to view buttons
                    document.querySelectorAll('.view-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const districtId = this.dataset.id;
                            loadDistrictDetails(districtId);
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading districts:', error);
                    alert('An error occurred while loading districts.');
                });
        }

        // Load district details function
        function loadDistrictDetails(id) {
            fetch(`<?php echo e(url("zonal-coordinator/districts/district")); ?>/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }
                    
                    // Update district information
                    document.getElementById('districtName').textContent = data.district;
                    document.getElementById('stateName').textContent = data.state;
                    document.getElementById('districtType').textContent = data.status || 'Not specified';
                    
                    // Update scientist information
                    const scientistInfo = document.getElementById('scientistInfo');
                    
                    if (data.scientist) {
                        // Fetch scientist details
                        fetch(`<?php echo e(url("zonal-coordinator/scientists/get-by-email")); ?>/${encodeURIComponent(data.scientist)}`)
                            .then(response => response.json())
                            .then(scientist => {
                                if (scientist.error) {
                                    scientistInfo.innerHTML = `<div class="alert alert-warning">Scientist found in database but details could not be loaded.</div>`;
                                    return;
                                }
                                
                                scientistInfo.innerHTML = `
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Name</th>
                                            <td>${scientist.name}</td>
                                        </tr>
                                        <tr>
                                            <th>Email</th>
                                            <td>${scientist.email}</td>
                                        </tr>
                                        <tr>
                                            <th>Phone</th>
                                            <td>${scientist.phone_number || 'Not provided'}</td>
                                        </tr>
                                        <tr>
                                            <th>Designation</th>
                                            <td>${scientist.designation || 'Not provided'}</td>
                                        </tr>
                                    </table>
                                `;
                            })
                            .catch(error => {
                                console.error('Error loading scientist details:', error);
                                scientistInfo.innerHTML = `<div class="alert alert-danger">Error loading scientist details.</div>`;
                            });
                    } else {
                        scientistInfo.innerHTML = `<div class="alert alert-warning">No scientist assigned to this district.</div>`;
                    }
                    
                    // Show the modal
                    districtModal.show();
                })
                .catch(error => {
                    console.error('Error loading district details:', error);
                    alert('An error occurred while loading district details.');
                });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/zonal-coordinator/districts/index.blade.php ENDPATH**/ ?>