<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Scientist Feedback Management')); ?></h5>
                    <a href="<?php echo e(route('district-state-coordinator.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Provide feedback to scientists on their completed action plans and events.</li>
                            <li>Rate their performance on preparedness, communication, engagement, and timeline adherence.</li>
                            <li>Your feedback helps scientists improve their future activities.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Event Title</th>
                                    <th>Scientist</th>
                                    <th>District</th>
                                    <th>Date</th>
                                    <th>Participants</th>
                                    <th>Feedback Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- Events will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Feedback Modal -->
                    <div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="feedbackModalLabel">Provide Feedback</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form id="feedbackForm">
                                    <div class="modal-body">
                                        <input type="hidden" id="actionPlanId" name="action_plan_id">
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <h6>Event Information</h6>
                                                <p><strong>Title:</strong> <span id="eventTitle"></span></p>
                                                <p><strong>Scientist:</strong> <span id="scientistName"></span></p>
                                                <p><strong>District:</strong> <span id="eventDistrict"></span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Participants</h6>
                                                <p><strong>Expected:</strong> <span id="expectedParticipants"></span></p>
                                                <p><strong>Actual:</strong> <span id="actualParticipants"></span></p>
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="preparednessRating" class="form-label">Event Preparedness (1-5)</label>
                                                    <select class="form-select" id="preparednessRating" name="preparedness_rating" required>
                                                        <option value="">Select Rating</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="communicationRating" class="form-label">Communication Effectiveness (1-5)</label>
                                                    <select class="form-select" id="communicationRating" name="communication_rating" required>
                                                        <option value="">Select Rating</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="engagementRating" class="form-label">Participant Engagement (1-5)</label>
                                                    <select class="form-select" id="engagementRating" name="engagement_rating" required>
                                                        <option value="">Select Rating</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="adherenceRating" class="form-label">Timeline Adherence (1-5)</label>
                                                    <select class="form-select" id="adherenceRating" name="adherence_rating" required>
                                                        <option value="">Select Rating</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="remarks" class="form-label">Additional Remarks</label>
                                            <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Provide additional feedback, suggestions, or comments..."></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="submit" class="btn btn-primary">Submit Feedback</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const eventsTableBody = document.getElementById('eventsTableBody');
        const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
        const feedbackForm = document.getElementById('feedbackForm');

        // Load events
        loadEvents();

        function loadEvents() {
            fetch('<?php echo e(route("district-state-coordinator.scientist-feedback.get-events")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    eventsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        eventsTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No completed events found for feedback.</td></tr>';
                        return;
                    }

                    data.forEach(event => {
                        const eventDate = new Date(event.planned_date);
                        const formattedDate = eventDate.toLocaleDateString();
                        const actualParticipants = (event.male_participants || 0) + (event.female_participants || 0) + (event.transgender_participants || 0);
                        const hasFeedback = event.zc_pvent_preparedness_feedback !== null;

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${event.title}</td>
                            <td>${event.scientist_name}</td>
                            <td>${event.state} - ${event.district}</td>
                            <td>${formattedDate}</td>
                            <td>${actualParticipants}/${event.expected_participants || 0}</td>
                            <td>
                                ${hasFeedback ? 
                                    '<span class="badge bg-success">Feedback Provided</span>' : 
                                    '<span class="badge bg-warning">Pending Feedback</span>'
                                }
                            </td>
                            <td>
                                <button class="btn btn-sm ${hasFeedback ? 'btn-info' : 'btn-primary'}" 
                                        onclick="provideFeedback(${event.id})">
                                    ${hasFeedback ? 'View/Edit Feedback' : 'Provide Feedback'}
                                </button>
                            </td>
                        `;
                        eventsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading events:', error);
                    alert('Failed to load events');
                });
        }

        // Provide feedback function
        window.provideFeedback = function(actionPlanId) {
            // First get the event details
            fetch(`<?php echo e(route("district-state-coordinator.scientist-feedback.feedback-details", ["id" => ":id"])); ?>`.replace(':id', actionPlanId))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Populate modal with event data
                    document.getElementById('actionPlanId').value = data.id;
                    document.getElementById('eventTitle').textContent = data.title;
                    document.getElementById('scientistName').textContent = data.scientist ? data.scientist.name : 'N/A';
                    document.getElementById('eventDistrict').textContent = data.district ? `${data.district.state} - ${data.district.district}` : 'N/A';
                    document.getElementById('expectedParticipants').textContent = data.expected_participants || 0;
                    document.getElementById('actualParticipants').textContent = data.actual_participants || 0;

                    // Populate existing feedback if available
                    if (data.has_feedback) {
                        document.getElementById('preparednessRating').value = data.preparedness_rating || '';
                        document.getElementById('communicationRating').value = data.communication_rating || '';
                        document.getElementById('engagementRating').value = data.engagement_rating || '';
                        document.getElementById('adherenceRating').value = data.adherence_rating || '';
                        document.getElementById('remarks').value = data.remarks || '';
                    } else {
                        // Clear form for new feedback
                        feedbackForm.reset();
                        document.getElementById('actionPlanId').value = data.id;
                    }

                    feedbackModal.show();
                })
                .catch(error => {
                    console.error('Error loading feedback details:', error);
                    alert('Failed to load feedback details');
                });
        };

        // Handle feedback form submission
        feedbackForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(feedbackForm);
            const data = Object.fromEntries(formData.entries());

            fetch('<?php echo e(route("district-state-coordinator.scientist-feedback.submit-feedback")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    feedbackModal.hide();
                    loadEvents(); // Reload the events table
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error submitting feedback:', error);
                alert('Failed to submit feedback');
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/scientist-feedback/index.blade.php ENDPATH**/ ?>