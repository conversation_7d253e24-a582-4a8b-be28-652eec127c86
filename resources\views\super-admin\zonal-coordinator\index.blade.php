@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Zonal/State Coordinator/Facilitator Management') }}</h5>
                    <div>
                        <button id="addCoordinatorBtn" class="btn btn-sm btn-success me-2">Add New Coordinator</button>
                        <a href="{{ route('super-admin.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Manage Zonal/State Coordinator/Facilitator who oversee multiple districts.</li>
                            <li>Each coordinator is responsible for a specific zone and can be assigned multiple districts.</li>
                            <li>Scientists in those districts will report to their respective zonal coordinator.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Zone</th>
                                    <th>Districts</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="coordinatorsTableBody">
                                <!-- Coordinators will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Coordinator Form Modal -->
                    <div class="modal fade" id="coordinatorModal" tabindex="-1" aria-labelledby="coordinatorModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="coordinatorModalLabel">Add New Zonal Coordinator</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="coordinatorForm">
                                        <input type="hidden" id="coordinatorId" name="id">
                                        <input type="hidden" id="isNew" name="is_new" value="true">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="name">Name:</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="email">Email:</label>
                                                    <input type="email" class="form-control" id="email" name="email" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="phone_number">Phone Number:</label>
                                                    <input type="text" class="form-control" id="phone_number" name="phone_number" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="designation">Designation:</label>
                                                    <input type="text" class="form-control" id="designation" name="designation" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="zone_name">Zone Name:</label>
                                                    <input type="text" class="form-control" id="zone_name" name="zone_name" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="password">Password:</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="password" name="password">
                                                        <button class="btn btn-outline-secondary" type="button" id="toggleZonalPassword">
                                                            <i class="fas fa-eye" id="zonalPasswordIcon"></i>
                                                        </button>
                                                    </div>
                                                    <small id="passwordHelpText" class="form-text text-muted">Leave blank to keep current password.</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3 mt-4">
                                            <label for="district_ids">Assigned Districts:</label>
                                            <div class="alert alert-info">
                                                <strong>Note:</strong> Zonal Coordinators can be assigned to multiple districts across different states.
                                            </div>
                                            <div id="districts_container">
                                                <!-- Districts will be grouped by state here -->
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="saveCoordinatorBtn">Save Coordinator</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this zonal coordinator? This action cannot be undone.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Coordinator</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const coordinatorsTableBody = document.getElementById('coordinatorsTableBody');
        const addCoordinatorBtn = document.getElementById('addCoordinatorBtn');
        const coordinatorForm = document.getElementById('coordinatorForm');
        const saveCoordinatorBtn = document.getElementById('saveCoordinatorBtn');
        const coordinatorModal = new bootstrap.Modal(document.getElementById('coordinatorModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const passwordHelpText = document.getElementById('passwordHelpText');
        const districtsContainer = document.getElementById('districts_container');
        let currentCoordinatorId = null;
        let availableDistricts = [];
        let selectedDistricts = [];

        // Password toggle functionality
        const toggleZonalPassword = document.getElementById('toggleZonalPassword');
        if (toggleZonalPassword) {
            toggleZonalPassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const passwordIcon = document.getElementById('zonalPasswordIcon');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    passwordIcon.classList.remove('fa-eye');
                    passwordIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    passwordIcon.classList.remove('fa-eye-slash');
                    passwordIcon.classList.add('fa-eye');
                }
            });
        }

        // Load coordinators
        loadCoordinators();

        // Load available districts
        loadAvailableDistricts();

        // Add coordinator button click
        addCoordinatorBtn.addEventListener('click', function() {
            resetForm();
            document.getElementById('coordinatorModalLabel').textContent = 'Add New Zonal Coordinator';
            document.getElementById('isNew').value = 'true';
            passwordHelpText.style.display = 'none';
            coordinatorModal.show();
        });

        // District checkbox change (updated to handle multi-state selection)
        document.addEventListener('change', function(e) {
            if (e.target && e.target.classList.contains('district-checkbox')) {
                const districtId = e.target.value;
                if (e.target.checked) {
                    if (!selectedDistricts.includes(districtId)) {
                        selectedDistricts.push(districtId);
                    }
                } else {
                    selectedDistricts = selectedDistricts.filter(id => id !== districtId);
                }
                // Update the display to show new counts
                updateDistrictsContainer();
            }
        });



        // Save coordinator button click
        saveCoordinatorBtn.addEventListener('click', function() {
            if (!coordinatorForm.checkValidity()) {
                coordinatorForm.reportValidity();
                return;
            }

            if (selectedDistricts.length === 0) {
                alert('Please select at least one district.');
                return;
            }

            const formData = new FormData(coordinatorForm);
            const data = {};

            formData.forEach((value, key) => {
                data[key] = value;
            });

            // Add selected districts
            data.district_ids = selectedDistricts;

            fetch('{{ route("super-admin.zonal-coordinator.save") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    coordinatorModal.hide();
                    loadCoordinators();
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving coordinator:', error);
                alert('An error occurred while saving the coordinator information.');
            });
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentCoordinatorId) {
                fetch(`{{ url("super-admin/zonal-coordinator/delete") }}/${currentCoordinatorId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadCoordinators();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting coordinator:', error);
                    alert('An error occurred while deleting the coordinator.');
                });
            }
        });

        // Load coordinators function
        function loadCoordinators() {
            fetch('{{ route("super-admin.zonal-coordinator.coordinators") }}')
                .then(response => response.json())
                .then(data => {
                    coordinatorsTableBody.innerHTML = '';

                    data.forEach(coordinator => {
                        const row = document.createElement('tr');

                        // Create district badges
                        let districtBadges = '';
                        if (coordinator.districts && coordinator.districts.length > 0) {
                            coordinator.districts.forEach(district => {
                                const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                                districtBadges += `<span class="badge ${badgeClass} me-1">${district.district} (${district.status})</span>`;
                            });
                        } else {
                            districtBadges = '<span class="text-muted">No districts assigned</span>';
                        }

                        row.innerHTML = `
                            <td>${coordinator.user.name}</td>
                            <td>${coordinator.user.email}</td>
                            <td>${coordinator.user.phone_number || ''}</td>
                            <td>${coordinator.zone_name}</td>
                            <td>${districtBadges}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="${coordinator.id}">Edit</button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${coordinator.id}">Delete</button>
                            </td>
                        `;

                        coordinatorsTableBody.appendChild(row);
                    });

                    // Add event listeners to edit buttons
                    document.querySelectorAll('.edit-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const coordinatorId = this.dataset.id;
                            loadCoordinatorDetails(coordinatorId);
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            currentCoordinatorId = this.dataset.id;
                            deleteConfirmModal.show();
                        });
                    });
                })
                .catch(error => console.error('Error loading coordinators:', error));
        }

        // Load available districts function
        function loadAvailableDistricts() {
            fetch('{{ route("super-admin.zonal-coordinator.available-districts") }}')
                .then(response => response.json())
                .then(data => {
                    availableDistricts = data;
                    updateDistrictSelect();
                })
                .catch(error => console.error('Error loading available districts:', error));
        }

        // Update districts container function
        function updateDistrictsContainer() {
            districtsContainer.innerHTML = '';

            // Group districts by state
            const stateDistricts = {};
            availableDistricts.forEach(district => {
                if (!stateDistricts[district.state]) {
                    stateDistricts[district.state] = [];
                }
                stateDistricts[district.state].push(district);
            });

            // Create a container for all states and their districts
            const allStatesContainer = document.createElement('div');
            allStatesContainer.className = 'accordion';
            allStatesContainer.id = 'statesAccordion';

            // Create cards for each state
            Object.keys(stateDistricts).sort().forEach((state, index) => {
                const stateCard = document.createElement('div');
                stateCard.className = 'accordion-item';

                const stateHeader = document.createElement('h2');
                stateHeader.className = 'accordion-header';
                stateHeader.id = `heading${index}`;

                const stateButton = document.createElement('button');
                stateButton.className = 'accordion-button collapsed';
                stateButton.type = 'button';
                stateButton.setAttribute('data-bs-toggle', 'collapse');
                stateButton.setAttribute('data-bs-target', `#collapse${index}`);
                stateButton.setAttribute('aria-expanded', 'false');
                stateButton.setAttribute('aria-controls', `collapse${index}`);

                // Count selected districts in this state
                const selectedInState = stateDistricts[state].filter(district =>
                    selectedDistricts.includes(district.id.toString())
                ).length;

                stateButton.innerHTML = `<strong>${state}</strong> <span class="badge bg-primary ms-2">${selectedInState}/${stateDistricts[state].length} selected</span>`;

                stateHeader.appendChild(stateButton);

                const stateCollapse = document.createElement('div');
                stateCollapse.id = `collapse${index}`;
                stateCollapse.className = 'accordion-collapse collapse';
                stateCollapse.setAttribute('aria-labelledby', `heading${index}`);
                stateCollapse.setAttribute('data-bs-parent', '#statesAccordion');

                const stateBody = document.createElement('div');
                stateBody.className = 'accordion-body';

                const districtList = document.createElement('div');
                districtList.className = 'row';

                stateDistricts[state].forEach(district => {
                    const districtCol = document.createElement('div');
                    districtCol.className = 'col-md-6 mb-2';

                    const districtCheck = document.createElement('div');
                    districtCheck.className = 'form-check';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'form-check-input district-checkbox';
                    checkbox.id = `district_${district.id}`;
                    checkbox.value = district.id;
                    checkbox.checked = selectedDistricts.includes(district.id.toString());

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = `district_${district.id}`;
                    label.textContent = `${district.district} (${district.status || 'No status'})`;

                    districtCheck.appendChild(checkbox);
                    districtCheck.appendChild(label);
                    districtCol.appendChild(districtCheck);
                    districtList.appendChild(districtCol);
                });

                stateBody.appendChild(districtList);
                stateCollapse.appendChild(stateBody);
                stateCard.appendChild(stateHeader);
                stateCard.appendChild(stateCollapse);
                allStatesContainer.appendChild(stateCard);
            });

            districtsContainer.appendChild(allStatesContainer);

            // Add a summary of selected districts
            if (selectedDistricts.length > 0) {
                const summary = document.createElement('div');
                summary.className = 'alert alert-success mt-3';
                summary.innerHTML = `<strong>Total Selected Districts:</strong> ${selectedDistricts.length}`;
                districtsContainer.appendChild(summary);
            }
        }

        // Load coordinator details function
        function loadCoordinatorDetails(id) {
            fetch(`{{ url("super-admin/zonal-coordinator/coordinator") }}/${id}`)
                .then(response => response.json())
                .then(coordinator => {
                    resetForm();

                    document.getElementById('coordinatorId').value = coordinator.id;
                    document.getElementById('name').value = coordinator.user.name;
                    document.getElementById('email').value = coordinator.user.email;
                    document.getElementById('phone_number').value = coordinator.user.phone_number || '';
                    document.getElementById('designation').value = coordinator.user.designation || '';
                    document.getElementById('zone_name').value = coordinator.zone_name;
                    document.getElementById('isNew').value = 'false';
                    document.getElementById('coordinatorModalLabel').textContent = 'Edit Zonal Coordinator';
                    passwordHelpText.style.display = 'block';

                    // Select assigned districts
                    if (coordinator.districts && coordinator.districts.length > 0) {
                        selectedDistricts = coordinator.districts.map(district => district.id.toString());
                        updateDistrictsContainer();
                    }

                    coordinatorModal.show();
                })
                .catch(error => console.error('Error loading coordinator details:', error));
        }

        // Reset form function
        function resetForm() {
            coordinatorForm.reset();
            document.getElementById('coordinatorId').value = '';

            // Reset district selection
            selectedDistricts = [];
            updateDistrictsContainer();
        }
    });
</script>
@endpush
