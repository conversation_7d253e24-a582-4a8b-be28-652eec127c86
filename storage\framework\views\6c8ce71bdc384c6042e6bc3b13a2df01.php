<?php $__env->startSection('content'); ?>
<style>
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .rating input {
        display: none;
    }

    .rating label {
        cursor: pointer;
        width: 30px;
        height: 30px;
        margin: 0 2px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ddd"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>') no-repeat center center;
        background-size: contain;
    }

    .rating input:checked ~ label,
    .rating label:hover,
    .rating label:hover ~ label {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffd700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
    }

    .rating label:hover,
    .rating label:hover ~ label {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }
</style>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Scientist Feedback</span>
                    
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Provide feedback to scientists in your assigned districts.</li>
                            <li>Rate scientists on a scale of 1-5 for various performance metrics.</li>
                            <li>Feedback can be linked to specific events or provided as general performance assessment.</li>
                        </ul>
                    </div>

                    <!-- Action Plans Table -->
                    <div class="table-responsive mb-4">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Scientist</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="actionPlansTableBody">
                                <!-- Action plans will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="feedbackModalLabel">Add New Feedback</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Inside the modal-body div -->
                <form id="feedbackForm" enctype="multipart/form-data">
                    <input type="hidden" id="action_plan_id" name="action_plan_id">

                    <h4 class="mb-4">Scientist Performance Rating</h4>
                    <!-- Event Preparedness rating -->
                    <div class="mb-3">
                        <label class="form-label">Event Preparedness</label>
                        <div class="rating">
                            <input type="radio" id="preparedness_rating_5" name="preparedness_rating" value="5" required>
                            <label for="preparedness_rating_5" title="5 stars"></label>
                            <input type="radio" id="preparedness_rating_4" name="preparedness_rating" value="4">
                            <label for="preparedness_rating_4" title="4 stars"></label>
                            <input type="radio" id="preparedness_rating_3" name="preparedness_rating" value="3">
                            <label for="preparedness_rating_3" title="3 stars"></label>
                            <input type="radio" id="preparedness_rating_2" name="preparedness_rating" value="2">
                            <label for="preparedness_rating_2" title="2 stars"></label>
                            <input type="radio" id="preparedness_rating_1" name="preparedness_rating" value="1">
                            <label for="preparedness_rating_1" title="1 star"></label>
                        </div>
                    </div>
                    <!-- Communication Effectiveness rating -->
                    <div class="mb-3">
                        <label class="form-label">Communication Effectiveness</label>
                        <div class="rating">
                            <input type="radio" id="communication_rating_5" name="communication_rating" value="5" required>
                            <label for="communication_rating_5" title="5 stars"></label>
                            <input type="radio" id="communication_rating_4" name="communication_rating" value="4">
                            <label for="communication_rating_4" title="4 stars"></label>
                            <input type="radio" id="communication_rating_3" name="communication_rating" value="3">
                            <label for="communication_rating_3" title="3 stars"></label>
                            <input type="radio" id="communication_rating_2" name="communication_rating" value="2">
                            <label for="communication_rating_2" title="2 stars"></label>
                            <input type="radio" id="communication_rating_1" name="communication_rating" value="1">
                            <label for="communication_rating_1" title="1 star"></label>
                        </div>
                    </div>
                    <!-- Participant Engagement rating -->
                    <div class="mb-3">
                        <label class="form-label">Participant Engagement</label>
                        <div class="rating">
                            <input type="radio" id="engagement_rating_5" name="engagement_rating" value="5" required>
                            <label for="engagement_rating_5" title="5 stars"></label>
                            <input type="radio" id="engagement_rating_4" name="engagement_rating" value="4">
                            <label for="engagement_rating_4" title="4 stars"></label>
                            <input type="radio" id="engagement_rating_3" name="engagement_rating" value="3">
                            <label for="engagement_rating_3" title="3 stars"></label>
                            <input type="radio" id="engagement_rating_2" name="engagement_rating" value="2">
                            <label for="engagement_rating_2" title="2 stars"></label>
                            <input type="radio" id="engagement_rating_1" name="engagement_rating" value="1">
                            <label for="engagement_rating_1" title="1 star"></label>
                        </div>
                    </div>
                    <!-- Timeline Adherence rating -->
                    <div class="mb-3">
                        <label class="form-label">Timeline Adherence</label>
                        <div class="rating">
                            <input type="radio" id="adherence_rating_5" name="adherence_rating" value="5" required>
                            <label for="adherence_rating_5" title="5 stars"></label>
                            <input type="radio" id="adherence_rating_4" name="adherence_rating" value="4">
                            <label for="adherence_rating_4" title="4 stars"></label>
                            <input type="radio" id="adherence_rating_3" name="adherence_rating" value="3">
                            <label for="adherence_rating_3" title="3 stars"></label>
                            <input type="radio" id="adherence_rating_2" name="adherence_rating" value="2">
                            <label for="adherence_rating_2" title="2 stars"></label>
                            <input type="radio" id="adherence_rating_1" name="adherence_rating" value="1">
                            <label for="adherence_rating_1" title="1 star"></label>
                        </div>
                    </div>
                    <!-- Remarks textarea -->
                    <div class="mb-3">
                        <label class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                    </div>

                    <h4 class="mb-4 mt-5">Event Report</h4>

                    <!-- Participant Counts -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Male Participants</label>
                            <input type="number" class="form-control" name="male_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Female Participants</label>
                            <input type="number" class="form-control" name="female_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Transgender Participants</label>
                            <input type="number" class="form-control" name="transgender_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">ST Participants</label>
                            <input type="number" class="form-control" name="st_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SC Participants</label>
                            <input type="number" class="form-control" name="sc_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">General Participants</label>
                            <input type="number" class="form-control" name="general_participants" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">OBC Participants</label>
                            <input type="number" class="form-control" name="obc_participants" required min="0">
                        </div>
                    </div>

                    <!-- Event Photos -->
                    <div class="mb-4">
                        <h5>Event Photos</h5>
                        <p class="text-muted">Each photo should be max 2MB</p>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Photo 1</label>
                                <input type="file" class="form-control" name="photo1" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Photo 2</label>
                                <input type="file" class="form-control" name="photo2" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Photo 3</label>
                                <input type="file" class="form-control" name="photo3" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Photo 4</label>
                                <input type="file" class="form-control" name="photo4" accept="image/*" required>
                            </div>
                        </div>
                    </div>

                    <!-- GIS Location -->
                    <div class="mb-4">
                        <label class="form-label">GIS Location *</label>
                        <div class="input-group">
                            <input type="text" class="form-control" name="gis_location" id="gis_location" required
                                   placeholder="e.g., 28.6139,77.2090"
                                   pattern="^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$">
                            <button type="button" class="btn btn-primary" onclick="getCurrentLocation()">Get Current Location</button>
                        </div>
                        <div class="form-text">Format: latitude,longitude (required)</div>
                    </div>

                    <!-- Event Success Rating -->
                    <div class="mb-3">
                        <label class="form-label">Event Success Rating (1-5 stars)</label>
                        <div class="rating">
                            <input type="radio" id="event_success_rating_5" name="event_success_rating" value="5" required>
                            <label for="event_success_rating_5" title="5 stars"></label>
                            <input type="radio" id="event_success_rating_4" name="event_success_rating" value="4">
                            <label for="event_success_rating_4" title="4 stars"></label>
                            <input type="radio" id="event_success_rating_3" name="event_success_rating" value="3">
                            <label for="event_success_rating_3" title="3 stars"></label>
                            <input type="radio" id="event_success_rating_2" name="event_success_rating" value="2">
                            <label for="event_success_rating_2" title="2 stars"></label>
                            <input type="radio" id="event_success_rating_1" name="event_success_rating" value="1">
                            <label for="event_success_rating_1" title="1 star"></label>
                        </div>
                    </div>

                    <!-- Feedback Fields -->
                    <div class="mb-3">
                        <label class="form-label">Challenges Faced</label>
                        <textarea class="form-control" name="challenges_faced" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Suggestions for Improvement</label>
                        <textarea class="form-control" name="suggestions_for_improvement" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Objectives Met</label>
                        <div id="objectives_container">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="objectives_met[]" placeholder="Enter an objective that was met" required>
                                <button type="button" class="btn btn-primary" onclick="addObjectiveField()">+</button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Self-assessed Learning Outcome</label>
                        <textarea class="form-control" name="self_assessed_learning_outcome" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveFeedbackBtn">Save Feedback</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
        const feedbackForm = document.getElementById('feedbackForm');

        // Load action plans when page loads
        loadActionPlans();

        // Function to load all action plans
        function loadActionPlans() {
            console.log('Loading action plans...');
            fetch('<?php echo e(route("zonal-coordinator.scientist-feedback.events")); ?>')
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Received data:', data);
                    if (!Array.isArray(data)) {
                        console.error('Invalid data format received:', data);
                        return;
                    }

                    const tbody = document.getElementById('actionPlansTableBody');
                    tbody.innerHTML = '';

                    data.forEach(plan => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${plan.title}</td>
                            <td>${plan.scientist ? plan.scientist.name : 'N/A'}</td>
                            <td>${plan.type}</td>
                            <td><span class="badge bg-${getStatusBadgeClass(plan.status)}">${plan.status}</span></td>
                            <td>
                                <button class="btn btn-sm ${plan.has_feedback ? 'btn-warning' : 'btn-primary'}"
                                        onclick="showFeedbackModal(${plan.id}, ${plan.has_feedback ? 'true' : 'false'})">
                                    ${plan.has_feedback ? 'Feedback Given' : 'Give Feedback'}
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading action plans:', error);
                });
        }

        function getStatusBadgeClass(status) {
            switch(status.toLowerCase()) {
                case 'completed':
                    return 'success';
                case 'in progress':
                    return 'primary';
                case 'cancelled':
                    return 'danger';
                case 'pending':
                    return 'warning';
                default:
                    return 'secondary';
            }
        }

        // Add event listeners
        document.getElementById('saveFeedbackBtn').addEventListener('click', function() {
            if (feedbackForm.checkValidity()) {
                saveFeedback();
            } else {
                feedbackForm.reportValidity();
            }
        });

        function showFeedbackModal(actionPlanId, hasFeedback) {
            console.log('Showing feedback modal for action plan:', actionPlanId, 'Has feedback:', hasFeedback);

            // Reset form
            feedbackForm.reset();

            // Set action plan ID
            document.getElementById('action_plan_id').value = actionPlanId;

            // If editing existing feedback, load the current values
            if (hasFeedback) {
                const plan = actionPlans.find(p => p.id === actionPlanId);
                if (plan && plan.feedback) {
                    // Load ratings
                    document.querySelector('input[name="preparedness_rating"][value="' + plan.feedback.preparedness + '"]').checked = true;
                    document.querySelector('input[name="communication_rating"][value="' + plan.feedback.communication + '"]').checked = true;
                    document.querySelector('input[name="engagement_rating"][value="' + plan.feedback.engagement + '"]').checked = true;
                    document.querySelector('input[name="adherence_rating"][value="' + plan.feedback.adherence + '"]').checked = true;

                    // Load other fields
                    document.getElementById('remarks').value = plan.feedback.remarks || '';
                    document.getElementById('male_participants').value = plan.feedback.male_participants;
                    document.getElementById('female_participants').value = plan.feedback.female_participants;
                    document.getElementById('transgender_participants').value = plan.feedback.transgender_participants;
                    document.getElementById('st_participants').value = plan.feedback.st_participants;
                    document.getElementById('sc_participants').value = plan.feedback.sc_participants;
                    document.getElementById('general_participants').value = plan.feedback.general_participants;
                    document.getElementById('obc_participants').value = plan.feedback.obc_participants;
                    document.getElementById('gis_location').value = plan.feedback.gis_location;
                    document.getElementById('event_success_rating').value = plan.feedback.event_success_rating;
                    document.getElementById('challenges_faced').value = plan.feedback.challenges_faced;
                    document.getElementById('suggestions_for_improvement').value = plan.feedback.suggestions_for_improvement;
                    document.getElementById('self_assessed_learning_outcome').value = plan.feedback.self_assessed_learning_outcome;

                    // Load objectives met
                    const objectivesContainer = document.getElementById('objectives_container');
                    objectivesContainer.innerHTML = ''; // Clear existing objectives
                    JSON.parse(plan.feedback.objectives_met).forEach(objective => {
                        const newField = document.createElement('div');
                        newField.className = 'input-group mb-2';
                        newField.innerHTML = `
                            <input type="text" class="form-control" name="objectives_met[]" value="${objective}" required>
                            <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">-</button>
                        `;
                        objectivesContainer.appendChild(newField);
                    });
                }
            }

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('feedbackModal'));
            modal.show();
        }

        function saveFeedback() {
            const formData = new FormData(feedbackForm);
            fetch('<?php echo e(route("zonal-coordinator.scientist-feedback.save")); ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Feedback saved successfully!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('feedbackModal'));
                    modal.hide();
                    loadActionPlans();
                } else {
                    alert('Failed to save feedback: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                // handle error
            });
        }

        // Make showFeedbackModal available globally
        window.showFeedbackModal = showFeedbackModal;

        // Add the functions inside the DOMContentLoaded event handler
        function addObjectiveField() {
            const container = document.getElementById('objectives_container');
            const newField = document.createElement('div');
            newField.className = 'input-group mb-2';
            newField.innerHTML = `
                <input type="text" class="form-control" name="objectives_met[]" placeholder="Enter an objective that was met" required>
                <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">-</button>
            `;
            container.appendChild(newField);
        }

        // Add getCurrentLocation function for GIS
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        document.getElementById('gis_location').value = `${lat},${lng}`;
                    },
                    function(error) {
                        alert('Error getting location: ' + error.message);
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }

        // Make functions available globally
        window.addObjectiveField = addObjectiveField;
        window.getCurrentLocation = getCurrentLocation;
    });
</script>







<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/zonal-coordinator/scientist-feedback/index.blade.php ENDPATH**/ ?>