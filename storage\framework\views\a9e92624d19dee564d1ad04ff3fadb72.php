<?php $__env->startSection('styles'); ?>
<style>
    /* Simple Star Rating CSS */
    .rating-container {
        margin: 20px 0;
    }
    .rating-count {
        margin-top: 10px;
        font-size: 14px;
        color: #666;
    }
    .star-rating {
        font-size: 0;
        white-space: nowrap;
        display: inline-block;
        width: 250px;
        height: 50px;
        overflow: hidden;
        position: relative;
        background: url('data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDIwIDIwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cG9seWdvbiBmaWxsPSIjREREREREIiBwb2ludHM9IjEwLDAgMTMuMDksNi41ODMgMjAsNy42MzkgMTUsMTIuNzY0IDE2LjE4LDIwIDEwLDE2LjU4MyAzLjgyLDIwIDUsMTIuNzY0IDAsNy42MzkgNi45MSw2LjU4MyAiLz48L3N2Zz4=');
        background-size: contain;
    }
    .star-rating i {
        opacity: 0;
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 20%;
        z-index: 1;
        background: url('data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDIwIDIwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cG9seWdvbiBmaWxsPSIjRkZERjg4IiBwb2ludHM9IjEwLDAgMTMuMDksNi41ODMgMjAsNy42MzkgMTUsMTIuNzY0IDE2LjE4LDIwIDEwLDE2LjU4MyAzLjgyLDIwIDUsMTIuNzY0IDAsNy42MzkgNi45MSw2LjU4MyAiLz48L3N2Zz4=');
        background-size: contain;
    }
    .star-rating input {
        opacity: 0;
        display: inline-block;
        width: 20%;
        height: 100%;
        margin: 0;
        padding: 0;
        z-index: 2;
        position: relative;
        cursor: pointer;
    }
    .star-rating input:hover + i,
    .star-rating input:checked + i {
        opacity: 1;
    }
    .star-rating i ~ i {
        width: 40%;
    }
    .star-rating i ~ i ~ i {
        width: 60%;
    }
    .star-rating i ~ i ~ i ~ i {
        width: 80%;
    }
    .star-rating i ~ i ~ i ~ i ~ i {
        width: 100%;
    }

    /* Action buttons styling to prevent text wrapping */
    .table td:last-child {
        min-width: 120px;
        width: 120px;
    }

    .d-grid .btn {
        white-space: nowrap;
        min-width: 100px;
        font-size: 0.875rem;
    }

    /* Ensure action column has enough space */
    .table th:last-child,
    .table td:last-child {
        width: 140px;
        min-width: 140px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><?php echo e(__('Action Plans')); ?></span>
                        <div>
                            <button id="createActionPlanBtn" class="btn btn-sm btn-primary me-2">Create New Action Plan</button>
                            <a href="<?php echo e(route('scientist.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Create action plans for various activities in your district.</li>
                            <li>After the event date has passed, submit a post-action report with participant details and photos.</li>
                            <li>You can create up to 2 action plans for each type: Training, Field Visit, Demonstration, and Awareness.</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex flex-column flex-md-row gap-2">
                            <div>
                                <label class="mb-1">Filter by Status:</label>
                                <div class="btn-group" role="group" aria-label="Filter action plans by status">
                                    <button type="button" class="btn btn-outline-secondary active" data-filter="all">All</button>
                                    <button type="button" class="btn btn-outline-warning" data-filter="planned">Planned</button>
                                    <button type="button" class="btn btn-outline-success" data-filter="completed">Completed</button>
                                    <button type="button" class="btn btn-outline-danger" data-filter="cancelled">Cancelled</button>
                                </div>
                            </div>

                            <div>
                                <label class="mb-1">Filter by Type:</label>
                                <div class="btn-group" role="group" aria-label="Filter action plans by type">
                                    <button type="button" class="btn btn-outline-secondary active" data-type-filter="all">All</button>
                                    <button type="button" class="btn btn-outline-primary" data-type-filter="training">Training</button>
                                    <button type="button" class="btn btn-outline-info" data-type-filter="field_visit">Field Visit</button>
                                    <button type="button" class="btn btn-outline-dark" data-type-filter="demonstration">Demonstration</button>
                                    <button type="button" class="btn btn-outline-secondary" data-type-filter="awareness">Awareness</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Location</th>
                                    <th>Date</th>
                                    <th>Expected Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="actionPlansTableBody">
                                <!-- Action plans will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Action Plan Modal -->
<div class="modal fade" id="actionPlanModal" tabindex="-1" aria-labelledby="actionPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionPlanModalLabel">Create Action Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="actionPlanForm">
                    <input type="hidden" id="actionPlanId" name="id">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="type" class="form-label">Action Plan Type</label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <option value="training">Farmer Training Programme</option>
                                <option value="field_visit">Field Visit</option>
                                <option value="demonstration">Demonstration/Transfer of Technology</option>
                                <option value="awareness">Awareness Programs</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="title" class="form-label">Title</label>
                            <select class="form-select" id="title_dropdown" name="title_dropdown" style="display: none;">
                                <option value="">Select Title</option>
                            </select>
                            <input type="text" class="form-control" id="title" name="title" placeholder="Enter custom title" style="display: none;" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" required>
                        </div>
                        <div class="col-md-6">
                            <label for="planned_date" class="form-label">Date</label>
                            <input type="datetime-local" class="form-control" id="planned_date" name="planned_date" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="expected_participants" class="form-label">Expected Participants</label>
                            <input type="number" class="form-control" id="expected_participants" name="expected_participants" min="1" required>
                        </div>
                        
                    </div>

                    
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="saveActionPlanBtn" class="btn btn-primary">Save Action Plan</button>
            </div>
        </div>
    </div>
</div>

<!-- Post-Action Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">Submit Post-Action Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="reportActionPlanId" name="id">

                    <div class="alert alert-info">
                        <p>Please provide the demographic breakdown of participants:</p>
                    </div>

                    <!-- Demographic fields remain unchanged -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="male_participants" class="form-label">Male Participants</label>
                            <input type="number" class="form-control" id="male_participants" name="male_participants" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label for="female_participants" class="form-label">Female Participants</label>
                            <input type="number" class="form-control" id="female_participants" name="female_participants" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label for="transgender_participants" class="form-label">Transgender Participants</label>
                            <input type="number" class="form-control" id="transgender_participants" name="transgender_participants" min="0" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="st_participants" class="form-label">ST Participants</label>
                            <input type="number" class="form-control" id="st_participants" name="st_participants" min="0" required>
                        </div>
                        <div class="col-md-3">
                            <label for="sc_participants" class="form-label">SC Participants</label>
                            <input type="number" class="form-control" id="sc_participants" name="sc_participants" min="0" required>
                        </div>
                        <div class="col-md-3">
                            <label for="general_participants" class="form-label">General Participants</label>
                            <input type="number" class="form-control" id="general_participants" name="general_participants" min="0" required>
                        </div>
                        <div class="col-md-3">
                            <label for="obc_participants" class="form-label">OBC Participants</label>
                            <input type="number" class="form-control" id="obc_participants" name="obc_participants" min="0" required>
                        </div>
                    </div>

                    <!-- Photo upload fields remain unchanged -->
                    <div class="mb-3">
                        <label class="form-label">Event Photos (4 photos required)</label>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label for="photo1" class="form-label">Photo 1</label>
                                <input type="file" class="form-control" id="photo1" name="photo1" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="photo2" class="form-label">Photo 2</label>
                                <input type="file" class="form-control" id="photo2" name="photo2" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="photo3" class="form-label">Photo 3</label>
                                <input type="file" class="form-control" id="photo3" name="photo3" accept="image/*" required>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="photo4" class="form-label">Photo 4</label>
                                <input type="file" class="form-control" id="photo4" name="photo4" accept="image/*" required>
                            </div>
                        </div>
                        <small class="text-muted">Each photo should be max 2MB</small>
                    </div>

                    <!-- GIS location field remains unchanged -->
                    <div class="mb-3">
                        <label for="gis_location" class="form-label">GIS Location <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="gis_location" name="gis_location" placeholder="e.g., 28.6139,77.2090" required pattern="^-?\d+(\.\d+)?,-?\d+(\.\d+)?$">
                            <button type="button" class="btn btn-primary" id="captureLocationBtn">
                                <i class="bi bi-geo-alt"></i> Capture Current Location
                            </button>
                        </div>
                        <small class="text-muted">Format: latitude,longitude (required)</small>
                        <div id="locationStatus" class="mt-2"></div>
                    </div>

                    <hr>
                    <h5 class="mb-3">Scientist Feedback with Metrics</h5>

                    <!-- Updated Star Rating -->
                    <div class="mb-3">
                        <label class="form-label fw-bold text-start">Event Success Rating (1-5 stars)</label>
                        <div class="rating-container">
                            <span class="star-rating">
                                <input type="radio" name="scientist_event_success_rating" value="1" required><i></i>
                                <input type="radio" name="scientist_event_success_rating" value="2"><i></i>
                                <input type="radio" name="scientist_event_success_rating" value="3"><i></i>
                                <input type="radio" name="scientist_event_success_rating" value="4"><i></i>
                                <input type="radio" name="scientist_event_success_rating" value="5"><i></i>
                            </span>
                            <div class="rating-count" id="ratingCount">0/5</div>
                        </div>
                    </div>

                    <!-- Remaining fields unchanged -->
                    <div class="mb-3">
                        <label for="scientist_challenges_faced" class="form-label">Challenges Faced</label>
                        <textarea class="form-control" id="scientist_challenges_faced" name="scientist_challenges_faced" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="scientist_suggestions_for_improvement" class="form-label">Suggestions for Improvement</label>
                        <textarea class="form-control" id="scientist_suggestions_for_improvement" name="scientist_suggestions_for_improvement" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Objectives Met</label>
                        <div class="card p-3 bg-light">
                            <div id="objectivesContainer">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control objectives-input" placeholder="Enter an objective that was met" required>
                                    <button type="button" class="btn btn-success add-objective">+</button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Add all objectives that were met during the event. Click + to add more objectives.</small>
                            </div>
                            <input type="hidden" name="scientist_objectives_met" id="scientist_objectives_met">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="scientist_self_assessed_learning_outcome" class="form-label">Self-assessed Learning Outcome</label>
                        <textarea class="form-control" id="scientist_self_assessed_learning_outcome" name="scientist_self_assessed_learning_outcome" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="submitReportBtn" class="btn btn-primary">Submit Report</button>
            </div>
        </div>
    </div>
</div>

<!-- View Action Plan Modal -->
<div class="modal fade" id="viewActionPlanModal" tabindex="-1" aria-labelledby="viewActionPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewActionPlanModalLabel">Action Plan Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="viewActionPlanBody">
                <!-- Action plan details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Add a console log to verify this script is loaded
    console.log('Action Plans script is loaded');

    document.addEventListener('DOMContentLoaded', function() {
        // Add debug logging
        console.log('Action Plans page loaded');

        // Elements
        const actionPlansTableBody = document.getElementById('actionPlansTableBody');
        if (!actionPlansTableBody) {
            console.error('actionPlansTableBody element not found');
        }

        // Filter buttons
        const statusFilterButtons = document.querySelectorAll('[data-filter]');
        const typeFilterButtons = document.querySelectorAll('[data-type-filter]');
        let currentStatusFilter = 'all';
        let currentTypeFilter = 'all';
        let actionPlansData = []; // Store all action plans data

        const actionPlanModalElement = document.getElementById('actionPlanModal');
        if (!actionPlanModalElement) {
            console.error('actionPlanModal element not found');
        }

        const reportModalElement = document.getElementById('reportModal');
        if (!reportModalElement) {
            console.error('reportModal element not found');
        }

        const viewActionPlanModalElement = document.getElementById('viewActionPlanModal');
        if (!viewActionPlanModalElement) {
            console.error('viewActionPlanModal element not found');
        }

        // Initialize Bootstrap modals
        let actionPlanModal, reportModal, viewActionPlanModal;

        try {
            actionPlanModal = new bootstrap.Modal(actionPlanModalElement);
            console.log('actionPlanModal initialized');
        } catch (error) {
            console.error('Failed to initialize actionPlanModal:', error);
        }

        try {
            reportModal = new bootstrap.Modal(reportModalElement);
            console.log('reportModal initialized');
        } catch (error) {
            console.error('Failed to initialize reportModal:', error);
        }

        try {
            viewActionPlanModal = new bootstrap.Modal(viewActionPlanModalElement);
            console.log('viewActionPlanModal initialized');
        } catch (error) {
            console.error('Failed to initialize viewActionPlanModal:', error);
        }

        const actionPlanForm = document.getElementById('actionPlanForm');
        const reportForm = document.getElementById('reportForm');
        const createActionPlanBtn = document.getElementById('createActionPlanBtn');
        const saveActionPlanBtn = document.getElementById('saveActionPlanBtn');
        const submitReportBtn = document.getElementById('submitReportBtn');

        // Initialize star rating
    function initializeStarRating() {
        const ratingInputs = document.querySelectorAll('.star-rating input');
        const ratingCount = document.getElementById('ratingCount');

        if (!ratingInputs.length || !ratingCount) {
            console.error('Star rating elements not found');
            return;
        }

        // Add change event to each radio button
        ratingInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = this.value;
                // Update rating count display
                ratingCount.textContent = `${value}/5`;
                console.log(`Star rating selected: ${value}`);
            });
        });

        // Reset stars when the modal is shown
        const reportModal = document.getElementById('reportModal');
        if (reportModal) {
            reportModal.addEventListener('show.bs.modal', function() {
                ratingInputs.forEach(input => {
                    input.checked = false;
                });
                ratingCount.textContent = '0/5';
            });
        }
    }

        initializeStarRating();

        // Load coordinators
        function loadCoordinators() {
            console.log('Loading coordinators...');
            fetch('<?php echo e(route("scientist.action-plans.coordinators")); ?>', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                credentials: 'same-origin'
            })
                .then(response => {
                    console.log('Coordinators response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Coordinators data received:', data);
                    const coordinatorsSelect = document.getElementById('required_coordinators');
                    coordinatorsSelect.innerHTML = '';

                    if (data.error) {
                        console.error('Error loading coordinators:', data.error);
                        coordinatorsSelect.innerHTML = `<option disabled>${data.error}</option>`;
                        return;
                    }

                    if (!Array.isArray(data)) {
                        console.error('Invalid coordinator data format received');
                        coordinatorsSelect.innerHTML = '<option disabled>Invalid data format received</option>';
                        return;
                    }

                    if (data.length === 0) {
                        console.log('No coordinators found');
                        coordinatorsSelect.innerHTML = '<option disabled>No District State Coordinators available</option>';
                        return;
                    }

                    console.log(`Adding ${data.length} coordinators to dropdown`);
                    data.forEach(coordinator => {
                        const option = document.createElement('option');
                        option.value = coordinator.id;
                        option.textContent = `${coordinator.name} (${coordinator.designation || 'No designation'})`;
                        coordinatorsSelect.appendChild(option);
                        console.log(`Added coordinator: ${coordinator.name} (ID: ${coordinator.id})`);
                    });

                    console.log('Coordinators loaded successfully');
                })
                .catch(error => {
                    console.error('Error loading coordinators:', error);
                    document.getElementById('required_coordinators').innerHTML = '<option disabled>Error loading coordinators</option>';
                });
        }

        // Load coordinators on page load
        loadCoordinators();

        // Load action plans
        function loadActionPlans() {
            console.log('Loading action plans...');

            fetch('<?php echo e(route("scientist.action-plans.get")); ?>')
                .then(response => {
                    console.log('Action plans response received:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Action plans data:', data);
                    console.log('Data type:', typeof data);
                    console.log('Is array:', Array.isArray(data));

                    if (data.error) {
                        console.error('Error in action plans data:', data.error);
                        actionPlansTableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">${data.error}</td></tr>`;
                        return;
                    }

                    if (!Array.isArray(data)) {
                        console.error('Data is not an array:', data);
                        actionPlansTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Invalid data format received from server</td></tr>';
                        return;
                    }

                    if (data.length === 0) {
                        console.log('No action plans found');
                        actionPlansTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No action plans found. Create your first action plan!</td></tr>';
                        return;
                    }

                    console.log(`Found ${data.length} action plans`);

                    // Store all action plans data
                    actionPlansData = data;

                    // Apply current filters
                    displayFilteredActionPlans(currentStatusFilter, currentTypeFilter);
                })
                .catch(error => {
                    console.error('Error loading action plans:', error);
                    actionPlansTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error loading action plans. Please try again later.</td></tr>';
                });
        }

        // Display filtered action plans
        function displayFilteredActionPlans(statusFilter, typeFilter) {
            // If only one parameter is provided, assume it's the status filter and keep the current type filter
            if (typeFilter === undefined) {
                typeFilter = currentTypeFilter;
            }

            console.log(`Filtering action plans by status: ${statusFilter}, type: ${typeFilter}`);
            console.log('Action plans data:', actionPlansData);

            // Clear the table
            actionPlansTableBody.innerHTML = '';

            // Check if we have data
            if (!actionPlansData || !Array.isArray(actionPlansData) || actionPlansData.length === 0) {
                console.error('No action plans data available for filtering');
                actionPlansTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No action plans found. Create your first action plan!</td></tr>';
                return;
            }

            // Filter the data by status
            let filteredData = statusFilter === 'all'
                ? actionPlansData
                : actionPlansData.filter(plan => plan.status === statusFilter);

            // Further filter by type if needed
            if (typeFilter !== 'all') {
                filteredData = filteredData.filter(plan => plan.type === typeFilter);
            }

            console.log(`Found ${filteredData.length} action plans with status "${statusFilter}" and type "${typeFilter}"`);

            if (filteredData.length === 0) {
                let message = `No action plans found`;
                if (statusFilter !== 'all') {
                    message += ` with status "${statusFilter}"`;
                }
                if (typeFilter !== 'all') {
                    message += ` and type "${typeFilter}"`;
                }
                actionPlansTableBody.innerHTML = `<tr><td colspan="7" class="text-center">${message}.</td></tr>`;
                return;
            }

            // Display the filtered data
            filteredData.forEach((plan, index) => {
                console.log(`Processing plan ${index + 1}:`, plan);
                const row = document.createElement('tr');

                        // Format date - use the date directly from the server without timezone conversion
                // The server date is already in Asia/Kolkata timezone
                const plannedDate = new Date(plan.planned_date);
                // Format as DD/MM/YYYY HH:MM AM/PM
                const formattedDate = plannedDate.toLocaleDateString('en-IN') + ' ' +
                    plannedDate.toLocaleTimeString('en-IN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    });

                // Format type
                let typeText = '';
                switch(plan.type) {
                    case 'training': typeText = 'Training Programme'; break;
                    case 'field_visit': typeText = 'Field Visit'; break;
                    case 'demonstration': typeText = 'Demonstration'; break;
                    case 'awareness': typeText = 'Awareness Program'; break;
                }

                // Format status
                let statusBadge = '';
                switch(plan.status) {
                    case 'planned':
                        statusBadge = '<span class="badge bg-warning">Planned</span>';
                        break;
                    case 'completed':
                        statusBadge = '<span class="badge bg-success">Completed</span>';
                        break;
                    case 'cancelled':
                        statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                        break;
                }

                // Action buttons with vertical spacing
                let actionButtons = `<div class="d-grid gap-2">
                    <button class="btn btn-sm btn-info view-plan" data-id="${plan.id}">View</button>`;

                if (plan.status === 'planned') {
                    // Check if planned date has passed
                    const now = new Date();
                    if (plannedDate <= now) {
                        actionButtons += `<button class="btn btn-sm btn-success report-plan" data-id="${plan.id}">Submit Report</button>`;
                    } else {
                        actionButtons += `<button class="btn btn-sm btn-primary edit-plan" data-id="${plan.id}">Edit</button>`;
                    }
                    actionButtons += `<button class="btn btn-sm btn-warning invite-dsc" data-id="${plan.id}">Invite DSCs</button>`;
                    actionButtons += `<button class="btn btn-sm btn-danger cancel-plan" data-id="${plan.id}">Cancel</button>`;
                }

                actionButtons += `</div>`;

                row.innerHTML = `
                    <td>${plan.title}</td>
                    <td>${typeText}</td>
                    <td>${plan.location}</td>
                    <td>${formattedDate}</td>
                    <td>${plan.expected_participants}</td>
                    <td>${statusBadge}</td>
                    <td>${actionButtons}</td>
                `;

                actionPlansTableBody.appendChild(row);
            });

            // Add event listeners to buttons
            console.log('Adding event listeners to buttons');

            const viewButtons = document.querySelectorAll('.view-plan');
            console.log(`Found ${viewButtons.length} view buttons`);

            viewButtons.forEach((btn, index) => {
                console.log(`Adding click listener to view button ${index + 1} with id ${btn.getAttribute('data-id')}`);
                btn.addEventListener('click', function(event) {
                    console.log(`View button clicked for plan ${this.getAttribute('data-id')}`);
                    event.preventDefault();
                    viewActionPlan(this.getAttribute('data-id'));
                });
            });

            const editButtons = document.querySelectorAll('.edit-plan');
            console.log(`Found ${editButtons.length} edit buttons`);

            editButtons.forEach((btn, index) => {
                console.log(`Adding click listener to edit button ${index + 1} with id ${btn.getAttribute('data-id')}`);
                btn.addEventListener('click', function(event) {
                    console.log(`Edit button clicked for plan ${this.getAttribute('data-id')}`);
                    event.preventDefault();
                    editActionPlan(this.getAttribute('data-id'));
                });
            });

            const reportButtons = document.querySelectorAll('.report-plan');
            console.log(`Found ${reportButtons.length} report buttons`);

            reportButtons.forEach((btn, index) => {
                console.log(`Adding click listener to report button ${index + 1} with id ${btn.getAttribute('data-id')}`);
                btn.addEventListener('click', function(event) {
                    console.log(`Report button clicked for plan ${this.getAttribute('data-id')}`);
                    event.preventDefault();
                    showReportModal(this.getAttribute('data-id'));
                });
            });

            // Invite DSC buttons
            const inviteDSCButtons = document.querySelectorAll('.invite-dsc');
            console.log(`Found ${inviteDSCButtons.length} invite DSC buttons`);

            inviteDSCButtons.forEach((btn, index) => {
                console.log(`Adding click listener to invite DSC button ${index + 1} with id ${btn.getAttribute('data-id')}`);
                btn.addEventListener('click', function(event) {
                    console.log(`Invite DSC button clicked for plan ${this.getAttribute('data-id')}`);
                    event.preventDefault();
                    const actionPlanId = this.getAttribute('data-id');
                    window.location.href = `<?php echo e(route('scientist.dsc-invitation', ['actionPlan' => ':id'])); ?>`.replace(':id', actionPlanId);
                });
            });

            // Cancel buttons are handled by the global click handler to avoid duplicate event listeners
            const cancelButtons = document.querySelectorAll('.cancel-plan');
            console.log(`Found ${cancelButtons.length} cancel buttons (handled by global click handler)`);
        }

        // Create new action plan
        createActionPlanBtn.addEventListener('click', function() {
            // Reset form
            actionPlanForm.reset();
            document.getElementById('actionPlanId').value = '';
            document.getElementById('actionPlanModalLabel').textContent = 'Create Action Plan';

            // Load coordinators
            loadCoordinators();

            // Show modal
            actionPlanModal.show();
        });

        // Save action plan
        saveActionPlanBtn.addEventListener('click', function() {
            if (!actionPlanForm.checkValidity()) {
                actionPlanForm.reportValidity();
                return;
            }

            // Disable save button
            saveActionPlanBtn.disabled = true;
            saveActionPlanBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

            // Get form data
            const formData = new FormData(actionPlanForm);
            const actionPlanId = document.getElementById('actionPlanId').value;

            // Convert to JSON
            const jsonData = {};
            formData.forEach((value, key) => {
                if (key === 'required_coordinators[]') {
                    if (!jsonData.required_coordinators) {
                        jsonData.required_coordinators = [];
                    }
                    jsonData.required_coordinators.push(value);
                } else {
                    jsonData[key] = value;
                }
            });

            // Determine if creating or updating
            const url = actionPlanId ?
                `<?php echo e(route("scientist.action-plans.update", ["id" => ":id"])); ?>`.replace(':id', actionPlanId) :
                '<?php echo e(route("scientist.action-plans.create")); ?>';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(jsonData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                } else {
                    alert(data.message);
                    actionPlanModal.hide();
                    loadActionPlans();
                }

                // Re-enable save button
                saveActionPlanBtn.disabled = false;
                saveActionPlanBtn.innerHTML = 'Save Action Plan';
            })
            .catch(error => {
                console.error('Error saving action plan:', error);
                alert('Error saving action plan. Please try again later.');

                // Re-enable save button
                saveActionPlanBtn.disabled = false;
                saveActionPlanBtn.innerHTML = 'Save Action Plan';
            });
        });

        // Edit action plan
        function editActionPlan(id) {
            fetch(`<?php echo e(route("scientist.action-plans.get-one", ["id" => ":id"])); ?>`.replace(':id', id), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Load coordinators first
                    loadCoordinators();

                    // Set form values
                    document.getElementById('actionPlanId').value = data.id;
                    document.getElementById('type').value = data.type;

                    // Update title options based on type
                    updateTitleOptions();

                    // Set title after options are populated
                    setTimeout(() => {
                        const titleDropdown = document.getElementById('title_dropdown');
                        const titleInput = document.getElementById('title');

                        // Check if the title exists in the dropdown options
                        const titleExists = Array.from(titleDropdown.options).some(option => option.value === data.title);

                        if (titleExists) {
                            // Title exists in dropdown, select it
                            titleDropdown.value = data.title;
                            titleInput.value = data.title;
                        } else {
                            // Title doesn't exist in dropdown, it's a custom title
                            titleDropdown.value = 'others';
                            titleDropdown.style.display = 'none';
                            titleInput.style.display = 'block';
                            titleInput.required = true;
                            titleInput.value = data.title;
                        }
                    }, 100);

                    document.getElementById('location').value = data.location;

                    // Format date for datetime-local input
                    // Create a date object from the server date (already in Asia/Kolkata timezone)
                    const plannedDate = new Date(data.planned_date);

                    // Format as YYYY-MM-DDTHH:MM (format required by datetime-local input)
                    // We need to pad single digits with leading zeros
                    const year = plannedDate.getFullYear();
                    const month = String(plannedDate.getMonth() + 1).padStart(2, '0');
                    const day = String(plannedDate.getDate()).padStart(2, '0');
                    const hours = String(plannedDate.getHours()).padStart(2, '0');
                    const minutes = String(plannedDate.getMinutes()).padStart(2, '0');

                    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
                    console.log('Formatted date for input:', formattedDate);

                    document.getElementById('planned_date').value = formattedDate;

                    document.getElementById('expected_participants').value = data.expected_participants;

                    // Set selected coordinators
                    setTimeout(() => {
                        const coordinatorsSelect = document.getElementById('required_coordinators');
                        if (data.required_coordinators) {
                            Array.from(coordinatorsSelect.options).forEach(option => {
                                option.selected = data.required_coordinators.includes(parseInt(option.value));
                            });
                        }
                    }, 500);

                    // Update modal title
                    document.getElementById('actionPlanModalLabel').textContent = 'Edit Action Plan';

                    // Show modal
                    actionPlanModal.show();
                })
                .catch(error => {
                    console.error('Error loading action plan details:', error);
                    alert('Error loading action plan details. Please try again later.');
                });
        }

        // View action plan
        function viewActionPlan(id) {
            fetch(`<?php echo e(route("scientist.action-plans.get-one", ["id" => ":id"])); ?>`.replace(':id', id), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                credentials: 'same-origin'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Format date - use the date directly from the server without timezone conversion
                    // The server date is already in Asia/Kolkata timezone
                    const plannedDate = new Date(data.planned_date);
                    // Format as DD/MM/YYYY HH:MM AM/PM
                    const formattedDate = plannedDate.toLocaleDateString('en-IN') + ' ' +
                        plannedDate.toLocaleTimeString('en-IN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        });

                    // Format type
                    let typeText = '';
                    switch(data.type) {
                        case 'training': typeText = 'Training Programme'; break;
                        case 'field_visit': typeText = 'Field Visit'; break;
                        case 'demonstration': typeText = 'Demonstration'; break;
                        case 'awareness': typeText = 'Awareness Program'; break;
                    }

                    // Format status
                    let statusBadge = '';
                    switch(data.status) {
                        case 'planned':
                            statusBadge = '<span class="badge bg-warning">Planned</span>';
                            break;
                        case 'completed':
                            statusBadge = '<span class="badge bg-success">Completed</span>';
                            break;
                        case 'cancelled':
                            statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                            break;
                    }

                    // Build HTML
                    let html = `
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Title:</strong> ${data.title}</p>
                                <p><strong>Type:</strong> ${typeText}</p>
                                <p><strong>Location:</strong> ${data.location}</p>
                                <p><strong>Date:</strong> ${formattedDate}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Status:</strong> ${statusBadge}</p>
                                <p><strong>Expected Participants:</strong> ${data.expected_participants}</p>
                            </div>
                        </div>
                    `;

                    // Add cancellation reason if status is cancelled
                    if (data.status === 'cancelled' && data.cancellation_reason) {
                        html += `
                            <div class="alert alert-danger text-black" style="background-color: rgba(220, 53, 69, 0.2);">
                                <strong>Reason for Cancellation:</strong><br>
                                ${data.cancellation_reason}
                            </div>
                        `;
                    }

                    // Add District State Coordinator responses if available
                    if (data.participation_requests && data.participation_requests.length > 0) {
                        html += `<hr><h5>District State Coordinator Responses of Invitation</h5>`;

                        data.participation_requests.forEach(request => {
                            let responseHtml = '';

                            if (request.status === 'accepted') {
                                responseHtml = `
                                    <div class="alert text-black" style="background-color: rgba(40, 167, 69, 0.2);">
                                        <strong>${request.coordinator.name}</strong> (${request.coordinator.designation}) has accepted your invitation.<br>
                                        <strong>Response:</strong> ${request.response_message || 'No message provided'}
                                    </div>
                                `;
                            } else if (request.status === 'rejected') {
                                responseHtml = `
                                    <div class="alert text-black" style="background-color: rgba(220, 53, 69, 0.2);">
                                        <strong>${request.coordinator.name}</strong> (${request.coordinator.designation}) has declined your invitation.<br>
                                        <strong>Response:</strong> ${request.response_message || 'No message provided'}
                                    </div>
                                `;
                            } else {
                                responseHtml = `
                                    <div class="alert text-black" style="background-color: rgba(255, 193, 7, 0.2);">
                                        <strong>${request.coordinator.name}</strong> (${request.coordinator.designation}) has not responded to your invitation yet.<br>
                                        <strong>Invitation Message:</strong> ${request.request_message || 'No message provided'}
                                    </div>
                                `;
                            }

                            html += responseHtml;
                        });
                    }

                    // Add report details if completed
                    if (data.status === 'completed') {
                        const totalParticipants = (data.male_participants || 0) +
                                                 (data.female_participants || 0) +
                                                 (data.transgender_participants || 0);

                        html += `
                            <hr>
                            <h5>Post-Action Report</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Total Participants:</strong> ${totalParticipants}</p>
                                    <p><strong>Male:</strong> ${data.male_participants || 0}</p>
                                    <p><strong>Female:</strong> ${data.female_participants || 0}</p>
                                    <p><strong>Transgender:</strong> ${data.transgender_participants || 0}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>ST Participants:</strong> ${data.st_participants || 0}</p>
                                    <p><strong>SC Participants:</strong> ${data.sc_participants || 0}</p>
                                    <p><strong>General Participants:</strong> ${data.general_participants || 0}</p>
                                    <p><strong>OBC Participants:</strong> ${data.obc_participants || 0}</p>
                                </div>
                            </div>
                        `;

                        // Add photos if available (check individual photo fields first, then fall back to photos array)
                        if (data.photo1 || data.photo2 || data.photo3 || data.photo4 || (data.photos && data.photos.length > 0)) {
                            html += '<h5>Event Photos</h5><div class="row mb-3">';

                            // Helper function to normalize photo path
                            function normalizePhotoPath(photoPath) {
                                if (!photoPath) return '';
                                // Remove any leading slashes or 'storage/' prefix
                                let cleanPath = photoPath.replace(/^\/+/, '').replace(/^storage\//, '');
                                // Use relative path instead of asset() to avoid domain issues
                                return `/storage/${cleanPath}`;
                            }

                            // Check individual photo fields first
                            if (data.photo1) {
                                const photoUrl = normalizePhotoPath(data.photo1);
                                console.log('Photo1 URL:', photoUrl, 'Original path:', data.photo1);
                                html += `
                                    <div class="col-md-3 mb-2">
                                        <img src="${photoUrl}" class="img-thumbnail" alt="Event Photo 1"
                                             onerror="console.error('Failed to load image:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';"
                                             onload="console.log('Successfully loaded image:', this.src);"
                                             style="max-height: 200px; object-fit: cover;">
                                        <div class="alert alert-warning" style="display: none;">
                                            <small>Image not available: ${photoUrl}</small>
                                        </div>
                                    </div>
                                `;
                            }

                            if (data.photo2) {
                                const photoUrl = normalizePhotoPath(data.photo2);
                                html += `
                                    <div class="col-md-3 mb-2">
                                        <img src="${photoUrl}" class="img-thumbnail" alt="Event Photo 2"
                                             onerror="console.error('Failed to load image:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';"
                                             onload="console.log('Successfully loaded image:', this.src);"
                                             style="max-height: 200px; object-fit: cover;">
                                        <div class="alert alert-warning" style="display: none;">
                                            <small>Image not available: ${photoUrl}</small>
                                        </div>
                                    </div>
                                `;
                            }

                            if (data.photo3) {
                                const photoUrl = normalizePhotoPath(data.photo3);
                                html += `
                                    <div class="col-md-3 mb-2">
                                        <img src="${photoUrl}" class="img-thumbnail" alt="Event Photo 3"
                                             onerror="console.error('Failed to load image:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';"
                                             onload="console.log('Successfully loaded image:', this.src);"
                                             style="max-height: 200px; object-fit: cover;">
                                        <div class="alert alert-warning" style="display: none;">
                                            <small>Image not available: ${photoUrl}</small>
                                        </div>
                                    </div>
                                `;
                            }

                            if (data.photo4) {
                                const photoUrl = normalizePhotoPath(data.photo4);
                                html += `
                                    <div class="col-md-3 mb-2">
                                        <img src="${photoUrl}" class="img-thumbnail" alt="Event Photo 4"
                                             onerror="console.error('Failed to load image:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';"
                                             onload="console.log('Successfully loaded image:', this.src);"
                                             style="max-height: 200px; object-fit: cover;">
                                        <div class="alert alert-warning" style="display: none;">
                                            <small>Image not available: ${photoUrl}</small>
                                        </div>
                                    </div>
                                `;
                            }

                            // Fall back to photos array if individual fields are not available
                            if (!data.photo1 && !data.photo2 && !data.photo3 && !data.photo4 && data.photos && data.photos.length > 0) {
                                data.photos.forEach((photo, index) => {
                                    const photoUrl = normalizePhotoPath(photo);
                                    html += `
                                        <div class="col-md-3 mb-2">
                                            <img src="${photoUrl}" class="img-thumbnail" alt="Event Photo ${index + 1}"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                                                 style="max-height: 200px; object-fit: cover;">
                                            <div class="alert alert-warning" style="display: none;">
                                                <small>Image not available</small>
                                            </div>
                                        </div>
                                    `;
                                });
                            }

                            html += '</div>';
                        }

                        // Add GIS location if available
                        if (data.gis_location) {
                            html += `<p><strong>GIS Location:</strong> ${data.gis_location}</p>`;
                        }

                        // Add Scientist Feedback with Metrics if available
                        if (data.scientist_event_success_rating || data.scientist_challenges_faced || data.scientist_suggestions_for_improvement ||
                            data.scientist_objectives_met || data.scientist_self_assessed_learning_outcome) {

                            html += `<hr><h5>Scientist Feedback with Metrics</h5>`;

                            // Event Success Rating
                            if (data.scientist_event_success_rating) {
                                // Create a star rating display similar to the input
                                html += `<p><strong>Event Success Rating:</strong>
                                    <div style="display: inline-block; position: relative; width: 250px; height: 50px; background: url('data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDIwIDIwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cG9seWdvbiBmaWxsPSIjREREREREIiBwb2ludHM9IjEwLDAgMTMuMDksNi41ODMgMjAsNy42MzkgMTUsMTIuNzY0IDE2LjE4LDIwIDEwLDE2LjU4MyAzLjgyLDIwIDUsMTIuNzY0IDAsNy42MzkgNi45MSw2LjU4MyAiLz48L3N2Zz4='); background-size: contain;">
                                        <div style="position: absolute; left: 0; top: 0; height: 100%; width: ${data.scientist_event_success_rating * 20}%; background: url('data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjBweCIgaGVpZ2h0PSIyMHB4IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDIwIDIwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cG9seWdvbiBmaWxsPSIjRkZERjg4IiBwb2ludHM9IjEwLDAgMTMuMDksNi41ODMgMjAsNy42MzkgMTUsMTIuNzY0IDE2LjE4LDIwIDEwLDE2LjU4MyAzLjgyLDIwIDUsMTIuNzY0IDAsNy42MzkgNi45MSw2LjU4MyAiLz48L3N2Zz4='); background-size: contain;"></div>
                                    </div>
                                    <span style="font-size: 16px; color: #666; margin-left: 10px;">(${data.scientist_event_success_rating}/5)</span>
                                </p>`;
                            }

                            // Challenges Faced
                            if (data.scientist_challenges_faced) {
                                html += `<p><strong>Challenges Faced:</strong> ${data.scientist_challenges_faced}</p>`;
                            }

                            // Suggestions for Improvement
                            if (data.scientist_suggestions_for_improvement) {
                                html += `<p><strong>Suggestions for Improvement:</strong> ${data.scientist_suggestions_for_improvement}</p>`;
                            }

                            // Skip displaying objectives

                            // Self-assessed Learning Outcome
                            if (data.scientist_self_assessed_learning_outcome) {
                                html += `<p><strong>Self-assessed Learning Outcome:</strong> ${data.scientist_self_assessed_learning_outcome}</p>`;
                            }
                        }
                    }

                    document.getElementById('viewActionPlanBody').innerHTML = html;
                    viewActionPlanModal.show();
                })
                .catch(error => {
                    console.error('Error loading action plan details:', error);
                    alert('Error loading action plan details. Please try again later.');
                });
        }

        // Show report modal
        function showReportModal(id) {
            console.log('showReportModal called with id:', id);

            // Check if reportForm exists
            if (!reportForm) {
                console.error('reportForm not found');
                return;
            }

            // Check if reportModal exists
            if (!reportModal) {
                console.error('reportModal not found');
                return;
            }

            // Reset form
            reportForm.reset();

            // Check if reportActionPlanId exists
            const reportActionPlanIdElement = document.getElementById('reportActionPlanId');
            if (!reportActionPlanIdElement) {
                console.error('reportActionPlanId element not found');
                return;
            }

            reportActionPlanIdElement.value = id;

            // Set up location capture button
            const captureLocationBtn = document.getElementById('captureLocationBtn');
            const gisLocationField = document.getElementById('gis_location');
            const locationStatus = document.getElementById('locationStatus');

            if (captureLocationBtn && gisLocationField && locationStatus) {
                captureLocationBtn.addEventListener('click', function() {
                    locationStatus.innerHTML = '<div class="alert alert-info">Getting your location...</div>';

                    if (navigator.geolocation) {
                        captureLocationBtn.disabled = true;

                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                const lat = position.coords.latitude;
                                const lng = position.coords.longitude;
                                gisLocationField.value = `${lat},${lng}`;

                                locationStatus.innerHTML = '<div class="alert alert-success">Location captured successfully!</div>';
                                captureLocationBtn.disabled = false;
                            },
                            function(error) {
                                console.error('Error capturing location:', error);

                                let errorMessage = 'Failed to capture location.';
                                switch(error.code) {
                                    case error.PERMISSION_DENIED:
                                        errorMessage = 'Location permission denied. Please enable location access in your browser.';
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        errorMessage = 'Location information is unavailable.';
                                        break;
                                    case error.TIMEOUT:
                                        errorMessage = 'Location request timed out.';
                                        break;
                                }

                                locationStatus.innerHTML = `<div class="alert alert-danger">${errorMessage}</div>`;
                                captureLocationBtn.disabled = false;
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 0
                            }
                        );
                    } else {
                        locationStatus.innerHTML = '<div class="alert alert-danger">Geolocation is not supported by this browser.</div>';
                    }
                });
            }

            // Show modal
            try {
                reportModal.show();
                console.log('Report modal shown successfully');
            } catch (error) {
                console.error('Error showing report modal:', error);
            }
        }

        // Add objective field
        function addObjectiveField() {
            const newField = document.createElement('div');
            newField.className = 'input-group mb-2';
            newField.innerHTML = `
                <input type="text" class="form-control objectives-input" placeholder="Enter an objective that was met" required>
                <button type="button" class="btn btn-danger remove-objective">
                    -
                </button>
            `;

            document.getElementById('objectivesContainer').appendChild(newField);

            // Focus the new input field
            const newInput = newField.querySelector('.objectives-input');
            if (newInput) {
                newInput.focus();
            }

            // Add event listener to remove button
            newField.querySelector('.remove-objective').addEventListener('click', function() {
                // Add a fade-out animation
                newField.style.transition = 'opacity 0.3s';
                newField.style.opacity = '0';

                // Remove after animation completes
                setTimeout(() => {
                    document.getElementById('objectivesContainer').removeChild(newField);
                }, 300);
            });
        }

        // Add event listener to add objective button
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('add-objective')) {
                addObjectiveField();
            }
        });

        // Submit report
        submitReportBtn.addEventListener('click', function() {
            console.log('Submit report button clicked');

            if (!reportForm.checkValidity()) {
                console.log('Form validation failed');
                reportForm.reportValidity();
                return;
            }

            // Check if all 4 photos are selected
            const photo1 = document.getElementById('photo1');
            const photo2 = document.getElementById('photo2');
            const photo3 = document.getElementById('photo3');
            const photo4 = document.getElementById('photo4');

            console.log('Photos selected:', {
                photo1: photo1.files.length > 0,
                photo2: photo2.files.length > 0,
                photo3: photo3.files.length > 0,
                photo4: photo4.files.length > 0
            });

            if (!photo1.files.length || !photo2.files.length || !photo3.files.length || !photo4.files.length) {
                alert('Please select all 4 photos.');
                return;
            }

            // Validate GIS location
            const gisLocation = document.getElementById('gis_location').value.trim();
            if (!gisLocation) {
                alert('Please capture or enter your GIS location.');
                document.getElementById('gis_location').focus();
                return;
            }

            // Check if GIS location is in the correct format (latitude,longitude)
            const gisPattern = /^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/;
            if (!gisPattern.test(gisLocation)) {
                alert('Please enter a valid GIS location in the format: latitude,longitude (e.g., 28.6139,77.2090)');
                document.getElementById('gis_location').focus();
                return;
            }

            // Validate star rating
            const eventSuccessRating = document.querySelector('.star-rating input:checked');
            if (!eventSuccessRating) {
                alert('Please select an event success rating (1-5 stars).');
                document.querySelector('.star-rating').scrollIntoView();
                return;
            }

            // Validate objectives
            const objectives = document.querySelectorAll('.objectives-input');
            let hasObjectives = false;
            let objectivesArray = [];
            objectives.forEach(obj => {
                if (obj.value.trim()) {
                    hasObjectives = true;
                    objectivesArray.push(obj.value.trim());
                }
            });

            if (!hasObjectives) {
                alert('Please enter at least one objective that was met.');
                return;
            }

            // Set scientist_objectives_met field - convert array to JSON string
            const scientistObjectivesMet = JSON.stringify(objectivesArray);
            console.log('Setting scientist_objectives_met to:', scientistObjectivesMet);
            document.getElementById('scientist_objectives_met').value = scientistObjectivesMet;

            // Disable submit button
            submitReportBtn.disabled = true;
            submitReportBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

            // Get form data
            const formData = new FormData(reportForm);

            // Log form data for debugging
            console.log('Form data prepared for submission:');
            for (let pair of formData.entries()) {
                if (pair[0] === 'scientist_objectives_met') {
                    console.log(pair[0] + ': ' + pair[1] + ' (type: ' + typeof pair[1] + ')');
                } else if (pair[0] !== 'photos[]' && pair[0] !== 'photo1' && pair[0] !== 'photo2' && pair[0] !== 'photo3' && pair[0] !== 'photo4') {
                    console.log(pair[0] + ': ' + pair[1] + ' (type: ' + typeof pair[1] + ')');
                } else {
                    console.log(pair[0] + ': ' + (pair[1].name || pair[1]) + ' (file)');
                }
            }

            console.log('Submitting report form with data:', {
                id: document.getElementById('reportActionPlanId').value,
                photos: {
                    photo1: photo1.files.length > 0 ? photo1.files[0].name : 'none',
                    photo2: photo2.files.length > 0 ? photo2.files[0].name : 'none',
                    photo3: photo3.files.length > 0 ? photo3.files[0].name : 'none',
                    photo4: photo4.files.length > 0 ? photo4.files[0].name : 'none'
                }
            });

            // Make sure scientist_objectives_met is a string
            const scientistObjectivesMetField = document.getElementById('scientist_objectives_met');
            if (scientistObjectivesMetField && typeof scientistObjectivesMetField.value !== 'string') {
                scientistObjectivesMetField.value = JSON.stringify(scientistObjectivesMetField.value);
            }

            // Create a new XMLHttpRequest for better file upload handling
            const xhr = new XMLHttpRequest();
            const url = `<?php echo e(route("scientist.action-plans.report", ["id" => ":id"])); ?>`.replace(':id', document.getElementById('reportActionPlanId').value);

            xhr.open('POST', url, true);

            // Set up event handlers
            xhr.onload = function() {
                console.log('XHR response received:', xhr.status, xhr.responseText);

                // Re-enable submit button
                submitReportBtn.disabled = false;
                submitReportBtn.innerHTML = 'Submit Report';

                try {
                    // Try to parse the response as JSON
                    const data = JSON.parse(xhr.responseText);

                    if (data.error) {
                        alert(data.error);
                    } else {
                        alert(data.message || 'Report submitted successfully');
                        reportModal.hide();
                        loadActionPlans();
                    }
                } catch (e) {
                    // If the response is not JSON, check if it's a redirect
                    if (xhr.status >= 200 && xhr.status < 300) {
                        alert('Report submitted successfully');
                        reportModal.hide();
                        loadActionPlans();
                    } else {
                        console.error('Error parsing response:', e);
                        alert('Error submitting report. Please try again later.');
                    }
                }
            };

            xhr.onerror = function() {
                console.error('XHR error:', xhr.status, xhr.statusText);
                alert('Error submitting report. Please try again later.');

                // Re-enable submit button
                submitReportBtn.disabled = false;
                submitReportBtn.innerHTML = 'Submit Report';
            };

            // Send the form data
            xhr.send(formData);
        });

        // Cancel action plan
        function cancelActionPlan(id) {
            if (!confirm('Are you sure you want to cancel this action plan? This cannot be undone.')) {
                return;
            }

            // Prompt for cancellation reason
            const cancellationReason = prompt('Please provide a reason for cancellation:', '');

            // Check if user provided a reason
            if (cancellationReason === null) {
                // User clicked cancel on the prompt
                return;
            }

            if (!cancellationReason.trim()) {
                alert('Please provide a reason for cancellation.');
                return cancelActionPlan(id); // Ask again
            }

            // Prepare data with cancellation reason
            const data = {
                cancellation_reason: cancellationReason.trim()
            };

            fetch(`<?php echo e(route("scientist.action-plans.cancel", ["id" => ":id"])); ?>`.replace(':id', id), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                } else {
                    alert(data.message);
                    loadActionPlans();
                }
            })
            .catch(error => {
                console.error('Error cancelling action plan:', error);
                alert('Error cancelling action plan. Please try again later.');
            });
        }

        // Set up status filter button event listeners
        statusFilterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all status buttons
                statusFilterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Get filter value
                const statusFilter = this.getAttribute('data-filter');
                currentStatusFilter = statusFilter;

                // Apply filters
                displayFilteredActionPlans(statusFilter, currentTypeFilter);
            });
        });

        // Set up type filter button event listeners
        typeFilterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all type buttons
                typeFilterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Get filter value
                const typeFilter = this.getAttribute('data-type-filter');
                currentTypeFilter = typeFilter;

                // Apply filters
                displayFilteredActionPlans(currentStatusFilter, typeFilter);
            });
        });

        // Load action plans on page load
        loadActionPlans();

        // Add direct event listener to the create button
        createActionPlanBtn.addEventListener('click', function(event) {
            console.log('Create button clicked directly');
        });

        // Add a global click handler to debug all clicks
        document.addEventListener('click', function(event) {
            console.log('Click detected on:', event.target);

            // Check if the click was on an action plan button
            if (event.target.classList.contains('view-plan')) {
                console.log('View plan button clicked via global handler');
                event.preventDefault();
                viewActionPlan(event.target.getAttribute('data-id'));
            } else if (event.target.classList.contains('edit-plan')) {
                console.log('Edit plan button clicked via global handler');
                event.preventDefault();
                editActionPlan(event.target.getAttribute('data-id'));
            } else if (event.target.classList.contains('report-plan')) {
                console.log('Report plan button clicked via global handler');
                event.preventDefault();
                showReportModal(event.target.getAttribute('data-id'));
            } else if (event.target.classList.contains('cancel-plan')) {
                console.log('Cancel plan button clicked via global handler');
                event.preventDefault();
                cancelActionPlan(event.target.getAttribute('data-id'));
            }
        });

        // Load dropdown configuration
        let dropdownConfig = {};

        // Load dropdown data from JSON file
        fetch('/js/action-plan-dropdowns.json')
            .then(response => response.json())
            .then(data => {
                dropdownConfig = data.dropdowns;
            })
            .catch(error => {
                console.error('Error loading dropdown configuration:', error);
            });

        // Handle action plan type change
        document.getElementById('type').addEventListener('change', function() {
            updateTitleOptions();
        });

        // Handle title dropdown change
        document.getElementById('title_dropdown').addEventListener('change', function() {
            const titleDropdown = document.getElementById('title_dropdown');
            const titleInput = document.getElementById('title');

            if (titleDropdown.value === 'others') {
                // Show custom input field
                titleDropdown.style.display = 'none';
                titleInput.style.display = 'block';
                titleInput.required = true;
                titleInput.focus();
                titleInput.value = '';
            } else if (titleDropdown.value) {
                // Set the selected title value
                titleInput.value = titleDropdown.value;
            }
        });

        // Update title options based on selected type
        function updateTitleOptions() {
            const typeSelect = document.getElementById('type');
            const titleDropdown = document.getElementById('title_dropdown');
            const titleInput = document.getElementById('title');

            const selectedType = typeSelect.value;

            // Hide both title controls initially
            titleDropdown.style.display = 'none';
            titleInput.style.display = 'none';
            titleInput.required = false;

            // Clear previous selections
            titleDropdown.innerHTML = '<option value="">Select Title</option>';
            titleInput.value = '';

            if (!selectedType) {
                return;
            }

            // Map action plan types to dropdown keys
            const typeMapping = {
                'training': 'FarmerTrainingProgram',
                'field_visit': 'FieldVisit',
                'awareness': 'AwarenessProgram',
                'demonstration': 'DemonstrationTransferOfTechnology'
            };

            const dropdownKey = typeMapping[selectedType];

            if (dropdownKey && dropdownConfig[dropdownKey]) {
                // Show title dropdown
                titleDropdown.style.display = 'block';
                titleDropdown.required = true;

                // Populate title options
                dropdownConfig[dropdownKey].forEach(title => {
                    const option = document.createElement('option');
                    option.value = title;
                    option.textContent = title;
                    titleDropdown.appendChild(option);
                });
            }
        }

        // Handle form reset for create new action plan
        const originalCreateHandler = createActionPlanBtn.onclick;
        createActionPlanBtn.addEventListener('click', function() {
            // Reset dropdown states
            document.getElementById('title_dropdown').style.display = 'none';
            document.getElementById('title').style.display = 'none';
            document.getElementById('title').required = false;
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/scientist/action-plans/index.blade.php ENDPATH**/ ?>