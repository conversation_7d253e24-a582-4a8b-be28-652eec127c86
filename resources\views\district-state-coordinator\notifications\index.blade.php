@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Notification Settings') }}</h5>
                    <a href="{{ route('district-state-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Configure your notification preferences for feedback and performance alerts.</li>
                            <li>Set thresholds for low rating alerts to stay informed about performance issues.</li>
                            <li>Enable daily or weekly summaries to get regular updates on district activities.</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Notification Preferences</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="{{ route('district-state-coordinator.notifications.update-settings') }}">
                                        @csrf
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="newFeedback" name="new_feedback" value="1" 
                                                       {{ $settings['new_feedback'] ?? false ? 'checked' : '' }}>
                                                <label class="form-check-label" for="newFeedback">
                                                    <strong>New Feedback Notifications</strong>
                                                    <br><small class="text-muted">Get notified when new participant feedback is received for events in your districts.</small>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="dailySummary" name="daily_summary" value="1" 
                                                       {{ $settings['daily_summary'] ?? false ? 'checked' : '' }}>
                                                <label class="form-check-label" for="dailySummary">
                                                    <strong>Daily Summary</strong>
                                                    <br><small class="text-muted">Receive a daily summary of activities and feedback in your districts.</small>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="weeklySummary" name="weekly_summary" value="1" 
                                                       {{ $settings['weekly_summary'] ?? false ? 'checked' : '' }}>
                                                <label class="form-check-label" for="weeklySummary">
                                                    <strong>Weekly Summary</strong>
                                                    <br><small class="text-muted">Receive a weekly performance summary and analytics report.</small>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="lowRatingAlerts" name="low_rating_alerts" value="1" 
                                                       {{ $settings['low_rating_alerts'] ?? false ? 'checked' : '' }}>
                                                <label class="form-check-label" for="lowRatingAlerts">
                                                    <strong>Low Rating Alerts</strong>
                                                    <br><small class="text-muted">Get alerted when feedback ratings fall below your specified threshold.</small>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="lowRatingThreshold" class="form-label">Low Rating Threshold (1-10)</label>
                                            <select class="form-select" id="lowRatingThreshold" name="low_rating_threshold">
                                                @for($i = 1; $i <= 10; $i++)
                                                    <option value="{{ $i }}" {{ ($settings['low_rating_threshold'] ?? 3) == $i ? 'selected' : '' }}>
                                                        {{ $i }} {{ $i <= 3 ? '(Poor)' : ($i <= 6 ? '(Average)' : '(Good)') }}
                                                    </option>
                                                @endfor
                                            </select>
                                            <small class="text-muted">You'll be notified when feedback ratings are at or below this level.</small>
                                        </div>

                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <button type="submit" class="btn btn-primary">Save Settings</button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="sendTestNotification()">Send Test Notification</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Notification Summary</h6>
                                </div>
                                <div class="card-body">
                                    <div id="notificationSummary">
                                        <p class="text-muted">Loading summary...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">Recent Notifications</h6>
                                </div>
                                <div class="card-body">
                                    <div id="recentNotifications">
                                        <p class="text-muted">Loading notifications...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load notification summary and recent notifications
        loadNotificationSummary();
        loadRecentNotifications();

        function loadNotificationSummary() {
            fetch('{{ route("district-state-coordinator.notifications.summary") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('notificationSummary').innerHTML = '<p class="text-danger">Error loading summary</p>';
                        return;
                    }

                    const summaryHtml = `
                        <div class="mb-2">
                            <strong>Today:</strong> ${data.stats.new_feedback_today} new feedback
                        </div>
                        <div class="mb-2">
                            <strong>This Week:</strong> ${data.stats.new_feedback_this_week} new feedback
                        </div>
                        <div class="mb-2">
                            <strong>Low Rating Alerts:</strong> ${data.stats.low_rating_alerts}
                        </div>
                        <hr>
                        <div class="small text-muted">
                            <div>✓ New Feedback: ${data.settings.new_feedback ? 'Enabled' : 'Disabled'}</div>
                            <div>✓ Daily Summary: ${data.settings.daily_summary ? 'Enabled' : 'Disabled'}</div>
                            <div>✓ Weekly Summary: ${data.settings.weekly_summary ? 'Enabled' : 'Disabled'}</div>
                            <div>✓ Low Rating Alerts: ${data.settings.low_rating_alerts ? 'Enabled' : 'Disabled'}</div>
                        </div>
                    `;

                    document.getElementById('notificationSummary').innerHTML = summaryHtml;
                })
                .catch(error => {
                    console.error('Error loading notification summary:', error);
                    document.getElementById('notificationSummary').innerHTML = '<p class="text-danger">Failed to load summary</p>';
                });
        }

        function loadRecentNotifications() {
            fetch('{{ route("district-state-coordinator.notifications.history") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('recentNotifications').innerHTML = '<p class="text-danger">Error loading notifications</p>';
                        return;
                    }

                    if (data.length === 0) {
                        document.getElementById('recentNotifications').innerHTML = '<p class="text-muted">No recent notifications</p>';
                        return;
                    }

                    let notificationsHtml = '';
                    data.slice(0, 5).forEach(notification => {
                        notificationsHtml += `
                            <div class="border-bottom pb-2 mb-2">
                                <div class="small">
                                    <strong>${notification.title}</strong>
                                    <br>
                                    <span class="text-muted">${notification.message}</span>
                                    <br>
                                    <small class="text-muted">${notification.formatted_date}</small>
                                </div>
                            </div>
                        `;
                    });

                    document.getElementById('recentNotifications').innerHTML = notificationsHtml;
                })
                .catch(error => {
                    console.error('Error loading recent notifications:', error);
                    document.getElementById('recentNotifications').innerHTML = '<p class="text-danger">Failed to load notifications</p>';
                });
        }

        window.sendTestNotification = function() {
            fetch('{{ route("district-state-coordinator.notifications.send-test") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert('Test notification sent successfully! Check your email or notification center.');
                } else if (data.error) {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error sending test notification:', error);
                alert('Failed to send test notification');
            });
        };
    });
</script>
@endpush
