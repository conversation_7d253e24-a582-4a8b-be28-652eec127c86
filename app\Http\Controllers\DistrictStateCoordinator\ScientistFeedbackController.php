<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ScientistFeedback;
use App\Models\User;
use App\Models\DistrictStateCoordinator;
use App\Models\StateData;
use App\Models\ActionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ScientistFeedbackController extends Controller
{
    /**
     * Display the scientist feedback page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('district-state-coordinator.scientist-feedback.index');
    }

    /**
     * Get all feedback entries created by the current district state coordinator.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeedback()
    {
        try {
            $user = Auth::user();
            $coordinator = DistrictStateCoordinator::where('user_id', $user->id)->first();

            if (!$coordinator) {
                Log::warning('District state coordinator record not found for user: ' . $user->id);
                return response()->json([]);
            }

            Log::info('Getting feedback for district state coordinator: ' . $coordinator->id);

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            $feedback = ActionPlan::whereIn('district_id', $districtIds)
                ->whereNotNull('zc_pvent_preparedness_feedback')
                ->with(['scientist:id,name,email'])
                ->get()
                ->map(function ($plan) {
                    return [
                        'id' => $plan->id,
                        'scientist' => [
                            'id' => $plan->scientist->id,
                            'name' => $plan->scientist->name,
                            'email' => $plan->scientist->email,
                        ],
                        'feedback' => $plan->zc_pvent_preparedness_feedback,
                    ];
                });

            Log::info('Found ' . $feedback->count() . ' feedback entries');
            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback'], 500);
        }
    }

    /**
     * Get action plans in the coordinator's districts.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEvents(Request $request)
    {
        try {
            $user = Auth::user();
            $coordinator = DistrictStateCoordinator::where('user_id', $user->id)->first();

            if (!$coordinator) {
                Log::warning('District state coordinator record not found for user: ' . $user->id);
                return response()->json([]);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get all action plans for scientists in these districts, including participant data from actionplan_feedback
            $actionPlans = DB::table('action_plans')
                ->join('users', 'action_plans.scientist_id', '=', 'users.id')
                ->join('state_data', 'action_plans.district_id', '=', 'state_data.id')
                ->leftJoin('actionplan_feedback', 'action_plans.id', '=', 'actionplan_feedback.action_plan_id')
                ->whereIn('state_data.id', $districtIds)
                ->where('action_plans.status', 'completed')
                ->select(
                    'action_plans.id',
                    'action_plans.title',
                    'action_plans.planned_date',
                    'action_plans.status',
                    'action_plans.location',
                    'action_plans.expected_participants',
                    'actionplan_feedback.male_participants',
                    'actionplan_feedback.female_participants',
                    'actionplan_feedback.transgender_participants',
                    'action_plans.zc_pvent_preparedness_feedback',
                    'action_plans.zc_communication_effectiveness_feedback',
                    'action_plans.zc_participant_engagement_feedback',
                    'action_plans.zc_timeline_adherence_feedback',
                    'action_plans.zc_remark',
                    'users.name as scientist_name',
                    'users.email as scientist_email',
                    'state_data.state',
                    'state_data.district'
                )
                ->orderBy('action_plans.planned_date', 'desc')
                ->get();

            return response()->json($actionPlans);
        } catch (\Exception $e) {
            Log::error('Error getting events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get events'], 500);
        }
    }

    /**
     * Submit feedback for an action plan.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitFeedback(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'action_plan_id' => 'required|exists:action_plans,id',
                'preparedness_rating' => 'required|integer|min:1|max:5',
                'communication_rating' => 'required|integer|min:1|max:5',
                'engagement_rating' => 'required|integer|min:1|max:5',
                'adherence_rating' => 'required|integer|min:1|max:5',
                'remarks' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()->first()], 422);
            }

            $user = Auth::user();
            $coordinator = DistrictStateCoordinator::where('user_id', $user->id)->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Verify the action plan belongs to one of the coordinator's districts
            $actionPlan = ActionPlan::whereIn('district_id', $districtIds)
                ->where('id', $request->action_plan_id)
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found in your districts'], 404);
            }

            // Update the action plan with feedback
            $actionPlan->update([
                'zc_pvent_preparedness_feedback' => $request->preparedness_rating,
                'zc_communication_effectiveness_feedback' => $request->communication_rating,
                'zc_participant_engagement_feedback' => $request->engagement_rating,
                'zc_timeline_adherence_feedback' => $request->adherence_rating,
                'zc_remark' => $request->remarks,
            ]);

            Log::info('Feedback submitted for action plan: ' . $actionPlan->id . ' by coordinator: ' . $coordinator->id);

            return response()->json([
                'message' => 'Feedback submitted successfully',
                'action_plan_id' => $actionPlan->id
            ]);

        } catch (\Exception $e) {
            Log::error('Error submitting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to submit feedback'], 500);
        }
    }

    /**
     * Get feedback details for a specific action plan.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeedbackDetails($id)
    {
        try {
            $user = Auth::user();
            $coordinator = DistrictStateCoordinator::where('user_id', $user->id)->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get the action plan with feedback using raw query, including participant data from actionplan_feedback table
            $actionPlan = DB::table('action_plans')
                ->select([
                    'action_plans.id',
                    'action_plans.title',
                    'action_plans.expected_participants',
                    'action_plans.zc_pvent_preparedness_feedback',
                    'action_plans.zc_communication_effectiveness_feedback',
                    'action_plans.zc_participant_engagement_feedback',
                    'action_plans.zc_timeline_adherence_feedback',
                    'action_plans.zc_remark',
                    'users.name as scientist_name',
                    'users.email as scientist_email',
                    'state_data.state',
                    'state_data.district',
                    'actionplan_feedback.male_participants',
                    'actionplan_feedback.female_participants',
                    'actionplan_feedback.transgender_participants',
                    DB::raw('(COALESCE(actionplan_feedback.male_participants, 0) + COALESCE(actionplan_feedback.female_participants, 0) + COALESCE(actionplan_feedback.transgender_participants, 0)) as actual_participants')
                ])
                ->join('users', 'action_plans.scientist_id', '=', 'users.id')
                ->join('state_data', 'action_plans.district_id', '=', 'state_data.id')
                ->leftJoin('actionplan_feedback', 'action_plans.id', '=', 'actionplan_feedback.action_plan_id')
                ->where('action_plans.id', $id)
                ->whereIn('action_plans.district_id', $districtIds)
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found in your districts'], 404);
            }



            $feedbackData = [
                'id' => $actionPlan->id,
                'title' => $actionPlan->title,
                'scientist' => [
                    'name' => $actionPlan->scientist_name,
                    'email' => $actionPlan->scientist_email
                ],
                'district' => [
                    'state' => $actionPlan->state,
                    'district' => $actionPlan->district
                ],
                'expected_participants' => $actionPlan->expected_participants ?? 0,
                'actual_participants' => $actionPlan->actual_participants ?? 0,
                'preparedness_rating' => $actionPlan->zc_pvent_preparedness_feedback,
                'communication_rating' => $actionPlan->zc_communication_effectiveness_feedback,
                'engagement_rating' => $actionPlan->zc_participant_engagement_feedback,
                'adherence_rating' => $actionPlan->zc_timeline_adherence_feedback,
                'remarks' => $actionPlan->zc_remark,
                'has_feedback' => !is_null($actionPlan->zc_pvent_preparedness_feedback),
            ];

            return response()->json($feedbackData);

        } catch (\Exception $e) {
            Log::error('Error getting feedback details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback details'], 500);
        }
    }
}
