@extends('layouts.app')

@section('title', 'District State Coordinator Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">District  Coordinator Dashboard</h5>
                </div>
                <div class="card-body">
                    @if(isset($error))
                        <div class="alert alert-danger">
                            {{ $error }}
                        </div>
                    @elseif(isset($welcomeMessage))
                        <div class="alert alert-info">
                            {{ $welcomeMessage }}
                        </div>
                    @else
                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">My Districts</h5>
                                        <h2 class="display-4">{{ count($districts) }}</h2>
                                        <p class="card-text">Districts under your coordination</p>
                                        <a href="{{ route('district-state-coordinator.districts.index') }}" class="btn btn-light">View Districts</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Pending Requests</h5>
                                        <h2 class="display-4">{{ $pendingRequests }}</h2>
                                        <p class="card-text">Participation requests awaiting your response</p>
                                        <a href="{{ route('district-state-coordinator.requests.index') }}" class="btn btn-light">View Requests</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Accepted Requests</h5>
                                        <h2 class="display-4">{{ $acceptedRequests }}</h2>
                                        <p class="card-text">Participation requests you've accepted</p>
                                        <a href="{{ route('district-state-coordinator.requests.accepted') }}" class="btn btn-light">View Accepted</a>
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h5 class="card-title">Performance</h5>
                                        <h2 class="display-4"><i class="fas fa-chart-line"></i></h2>
                                        <p class="card-text">Monitor district and scientist performance</p>
                                        <a href="{{ route('district-state-coordinator.performance.index') }}" class="btn btn-dark">View Performance</a>
                                    </div>
                                </div>
                            </div> --}}
                        </div>

                        <!-- Quick Access Cards -->
                        <div class="row mt-4">
                            {{-- <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Scientists Management</div>
                                    <div class="card-body">
                                        <p>View and manage scientists in your assigned districts.</p>
                                        <a href="{{ route('district-state-coordinator.scientists.index') }}" class="btn btn-primary">View Scientists</a>
                                    </div>
                                </div>
                            </div> --}}
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Events & Action Plans</div>
                                    <div class="card-body">
                                        <p>Monitor events and action plans in your districts.</p>
                                        <a href="{{ route('district-state-coordinator.events.index') }}" class="btn btn-primary">View Events</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Scientist Feedback</div>
                                    <div class="card-body">
                                        <p>Provide feedback to scientists on their performance.</p>
                                        <a href="{{ route('district-state-coordinator.scientist-feedback.index') }}" class="btn btn-primary">Manage Feedback</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Profile Management</div>
                                    <div class="card-body">
                                        <p>Update your expertise areas and profile information.</p>
                                        <a href="{{ route('district-state-coordinator.profile.index') }}" class="btn btn-primary">Manage Profile</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Feedback Analytics</div>
                                    <div class="card-body">
                                        <p>Analyze participant feedback and performance trends.</p>
                                        <a href="{{ route('district-state-coordinator.feedback-analytics.index') }}" class="btn btn-primary">View Analytics</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Notifications</div>
                                    <div class="card-body">
                                        <p>Configure notification settings and preferences.</p>
                                        <a href="{{ route('district-state-coordinator.notifications.index') }}" class="btn btn-primary">Manage Notifications</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Performance Dashboard</div>
                                    <div class="card-body">
                                        <p>Monitor district and scientist performance metrics.</p>
                                        <a href="{{ route('district-state-coordinator.performance.index') }}" class="btn btn-primary">View Performance</a>
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Your Assigned Districts</h5>
                                    </div>
                                    <div class="card-body">
                                        @if(count($districts) > 0)
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>State</th>
                                                            <th>District</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($districts as $district)
                                                            <tr>
                                                                <td>{{ $district->state }}</td>
                                                                <td>{{ $district->district }}</td>
                                                                <td>
                                                                    <span class="badge {{ $district->status == 'Pre-Cocoon' ? 'bg-info' : 'bg-warning' }}">
                                                                        {{ $district->status }}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        @else
                                            <div class="alert alert-info">
                                                No districts have been assigned to you yet.
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
