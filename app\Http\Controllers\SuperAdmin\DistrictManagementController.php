<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\StateData;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class DistrictManagementController extends Controller
{
    /**
     * Display the district management page.
     */
    public function index()
    {
        return view('super-admin.district-management.index');
    }

    /**
     * Get all unique states from the JSON file.
     */
    public function getStates()
    {
        Log::info('Getting all states from JSON file');

        try {
            // Read the JSON file
            $jsonContent = file_get_contents(storage_path('app/states_districts.json'));
            $data = json_decode($jsonContent, true);

            if (!$data || !isset($data['states'])) {
                Log::error('Invalid JSON structure in states_districts.json');
                return response()->json([]);
            }

            // Extract state names
            $states = collect($data['states'])->pluck('name')->toArray();

            Log::info('Found ' . count($states) . ' states from JSON file');
            return response()->json($states);
        } catch (\Exception $e) {
            Log::error('Error reading states from JSON file: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Get districts for a specific state.
     */
    public function getDistricts($state)
    {
        Log::info('Getting districts for state: ' . $state);

        try {
            // Read the JSON file
            $jsonContent = file_get_contents(storage_path('app/states_districts.json'));
            $data = json_decode($jsonContent, true);

            if (!$data || !isset($data['states'])) {
                Log::error('Invalid JSON structure in states_districts.json');
                return response()->json([]);
            }

            // Find the state in the JSON data
            $stateData = collect($data['states'])->firstWhere('name', $state);

            if (!$stateData || !isset($stateData['districts'])) {
                Log::error('State not found or no districts available for state: ' . $state);
                return response()->json([]);
            }

            // Get districts for the state
            $districts = collect($stateData['districts'])->map(function($district) {
                // Try to get district data from state_data table
                $stateData = StateData::where('id', $district['id'])->first();

                // Try to get scientist name
                $scientistName = null;
                if ($stateData && $stateData->scientist) {
                    $scientistName = User::where('email', $stateData->scientist)->value('name');
                }

                // Return a single entry for each district
                return [
                    'id' => $district['id'],
                    'name' => $district['name'],
                    'status' => $stateData ? $stateData->status : null,
                    'lgd_code' => $stateData ? $stateData->lgd_code : null,
                    'scientist' => $stateData ? $stateData->scientist : null,
                    'scientist_name' => $scientistName
                ];
            });

            Log::info('Found ' . count($districts) . ' districts for state: ' . $state);
            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error reading districts from JSON file: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Get district details.
     */
    public function getDistrictDetails($id)
    {
        Log::info('Getting district details for ID: ' . $id);

        try {
            // We're now using the original ID directly
            $originalId = $id;

            // Try to get district data from the state_data table
            $stateData = StateData::where('id', $originalId)->first();

            if ($stateData) {
                $district = [
                    'id' => $id,
                    'original_id' => $originalId,
                    'state' => $stateData->state,
                    'district' => $stateData->district,
                    'lgd_code' => $stateData->lgd_code,
                    'status' => $stateData->status,
                    'scientist' => $stateData->scientist,
                ];
            } else {
                // If not found in database, try to find in JSON file
                $jsonContent = file_get_contents(storage_path('app/states_districts.json'));
                $data = json_decode($jsonContent, true);

                $districtData = null;
                $stateName = null;

                foreach ($data['states'] as $state) {
                    foreach ($state['districts'] as $dist) {
                        if ($dist['id'] == $originalId) {
                            $districtData = $dist;
                            $stateName = $state['name'];
                            break 2;
                        }
                    }
                }

                if (!$districtData) {
                    Log::error('District with ID ' . $originalId . ' not found in JSON file');
                    return response()->json(['error' => 'District not found'], 404);
                }

                $district = [
                    'id' => $id,
                    'original_id' => $originalId,
                    'state' => $stateName,
                    'district' => $districtData['name'],
                    'lgd_code' => null,
                    'status' => null,
                    'scientist' => null,
                ];
            }

            $scientist = null;
            if (isset($district['scientist']) && $district['scientist']) {
                $scientist = User::where('email', $district['scientist'])
                    ->select('id', 'name', 'email', 'phone_number', 'designation')
                    ->first();
            }

            return response()->json([
                'district' => $district,
                'scientist' => $scientist
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting district details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get district details'], 500);
        }
    }

    /**
     * Save district type and scientist info.
     */
    public function saveDistrictScientist(Request $request)
    {
        $request->validate([
            'district_id' => 'required',
            'original_id' => 'required',
            'user_id' => 'nullable|integer|exists:users,id',
            'state_name' => 'required|string|max:100',
            'district_name' => 'required|string|max:255',
            'lgd_code' => 'nullable|string|max:50',
            'status' => 'required|string|in:Pre-Cocoon,Post-Cocoon',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'designation' => 'required|string|max:100',
            'password' => 'required_if:is_new,true|nullable|string|min:4',
        ]);

        // Start a transaction
        DB::beginTransaction();

        try {
            // Try to find district in state_data table
            $stateData = StateData::where('id', $request->original_id)->first();

            if (!$stateData) {
                // Create new state_data record if it doesn't exist
                $stateData = new StateData();
                $stateData->id = $request->original_id;
                $stateData->state = $request->state_name;
                $stateData->district = $request->district_name;
            }

            // Update district data
            $stateData->lgd_code = $request->lgd_code;
            $stateData->status = $request->status;
            $stateData->scientist = $request->email;

            $stateData->save();

            $user = null;

            // If user_id is provided, find user by ID (for updates)
            if ($request->filled('user_id')) {
                $user = User::find($request->user_id);

                // Check if email is being changed to an existing email
                if ($user && $user->email !== $request->email) {
                    $existingEmailUser = User::where('email', $request->email)
                        ->where('id', '!=', $user->id)
                        ->first();

                    if ($existingEmailUser) {
                        return response()->json(['error' => 'Email already exists for another user'], 422);
                    }
                }
            } else {
                // For new users, check if email already exists
                $existingEmailUser = User::where('email', $request->email)->first();
                if ($existingEmailUser) {
                    return response()->json(['error' => 'Email already exists'], 422);
                }
            }

            if ($user) {
                // Store old email to update district assignments if email changes
                $oldEmail = $user->email;

                // Update existing user
                $user->name = $request->name;
                $user->email = $request->email;
                $user->phone_number = $request->phone_number;
                $user->designation = $request->designation;

                // Update password if provided
                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                }

                $user->save();

                // If email changed, update district assignments in state_data table
                if ($oldEmail !== $request->email) {
                    \DB::table('state_data')
                        ->where('scientist', $oldEmail)
                        ->update(['scientist' => $request->email]);

                    Log::info('Updated district assignments for scientist email change', [
                        'old_email' => $oldEmail,
                        'new_email' => $request->email
                    ]);
                }
            } else {
                // Create new scientist user
                User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'designation' => $request->designation,
                    'password' => Hash::make($request->password),
                    'role' => 'scientist',
                ]);
            }

            // Commit the transaction
            DB::commit();

            return response()->json(['message' => 'District and scientist information saved successfully']);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            Log::error('Error saving district and scientist information: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save district and scientist information'], 500);
        }
    }
}
