<?php $__env->startSection('title', 'District State Coordinator Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">District  Coordinator Dashboard</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo e($error); ?>

                        </div>
                    <?php elseif(isset($welcomeMessage)): ?>
                        <div class="alert alert-info">
                            <?php echo e($welcomeMessage); ?>

                        </div>
                    <?php else: ?>
                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">My Districts</h5>
                                        <h2 class="display-4"><?php echo e(count($districts)); ?></h2>
                                        <p class="card-text">Districts under your coordination</p>
                                        <a href="<?php echo e(route('district-state-coordinator.districts.index')); ?>" class="btn btn-light">View Districts</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Pending Requests</h5>
                                        <h2 class="display-4"><?php echo e($pendingRequests); ?></h2>
                                        <p class="card-text">Participation requests awaiting your response</p>
                                        <a href="<?php echo e(route('district-state-coordinator.requests.index')); ?>" class="btn btn-light">View Requests</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Accepted Requests</h5>
                                        <h2 class="display-4"><?php echo e($acceptedRequests); ?></h2>
                                        <p class="card-text">Participation requests you've accepted</p>
                                        <a href="<?php echo e(route('district-state-coordinator.requests.accepted')); ?>" class="btn btn-light">View Accepted</a>
                                    </div>
                                </div>
                            </div>
                            
                        </div>

                        <!-- Quick Access Cards -->
                        <div class="row mt-4">
                            
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Events & Action Plans</div>
                                    <div class="card-body">
                                        <p>Monitor events and action plans in your districts.</p>
                                        <a href="<?php echo e(route('district-state-coordinator.events.index')); ?>" class="btn btn-primary">View Events</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Scientist Feedback</div>
                                    <div class="card-body">
                                        <p>Provide feedback to scientists on their performance.</p>
                                        <a href="<?php echo e(route('district-state-coordinator.scientist-feedback.index')); ?>" class="btn btn-primary">Manage Feedback</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header">Profile Management</div>
                                    <div class="card-body">
                                        <p>Update your expertise areas and profile information.</p>
                                        <a href="<?php echo e(route('district-state-coordinator.profile.index')); ?>" class="btn btn-primary">Manage Profile</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Your Assigned Districts</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php if(count($districts) > 0): ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>State</th>
                                                            <th>District</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $districts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $district): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td><?php echo e($district->state); ?></td>
                                                                <td><?php echo e($district->district); ?></td>
                                                                <td>
                                                                    <span class="badge <?php echo e($district->status == 'Pre-Cocoon' ? 'bg-info' : 'bg-warning'); ?>">
                                                                        <?php echo e($district->status); ?>

                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-info">
                                                No districts have been assigned to you yet.
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/dashboard.blade.php ENDPATH**/ ?>