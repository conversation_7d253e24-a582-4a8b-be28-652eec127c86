<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DistrictStateCoordinator extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'district_state_coordinators';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'designation',
        'expertise_areas',
        'role',
        'user_id',
        'notification_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expertise_areas' => 'array',
        'notification_settings' => 'array',
    ];

    /**
     * Get default notification settings.
     *
     * @return array
     */
    public static function getDefaultNotificationSettings(): array
    {
        return [
            'new_feedback' => true,
            'daily_summary' => true,
            'weekly_summary' => true,
            'low_rating_alerts' => true,
            'low_rating_threshold' => 3,
        ];
    }

    /**
     * Get the districts associated with this coordinator.
     */
    public function districts()
    {
        return $this->belongsToMany(StateData::class, 'district_state_coordinator_mapping', 'district_state_coordinator_id', 'district_id');
    }

    /**
     * Get the user associated with this coordinator.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get participation requests received by this coordinator.
     */
    public function participationRequests()
    {
        return $this->hasMany(ParticipationRequest::class, 'coordinator_id', 'user_id');
    }
}
