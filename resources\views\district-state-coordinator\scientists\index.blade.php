@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Scientists in My Districts') }}</h5>
                    <a href="{{ route('district-state-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all scientists assigned to districts under your coordination.</li>
                            <li>Click on a scientist to view their details and performance.</li>
                            <li>Monitor their activities and provide guidance as needed.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Designation</th>
                                    <th>Districts</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="scientistsTableBody">
                                <!-- Scientists will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Scientist Details Modal -->
                    <div class="modal fade" id="scientistModal" tabindex="-1" aria-labelledby="scientistModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="scientistModalLabel">Scientist Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Personal Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Name</th>
                                                    <td id="scientistName"></td>
                                                </tr>
                                                <tr>
                                                    <th>Email</th>
                                                    <td id="scientistEmail"></td>
                                                </tr>
                                                <tr>
                                                    <th>Phone</th>
                                                    <td id="scientistPhone"></td>
                                                </tr>
                                                <tr>
                                                    <th>Designation</th>
                                                    <td id="scientistDesignation"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Assigned Districts</h6>
                                            <div id="scientistDistricts">
                                                <!-- Districts will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const scientistsTableBody = document.getElementById('scientistsTableBody');
        const scientistModal = new bootstrap.Modal(document.getElementById('scientistModal'));

        // Load scientists
        loadScientists();

        // Load scientists function
        function loadScientists() {
            fetch('{{ route("district-state-coordinator.scientists.get-scientists") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    scientistsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        scientistsTableBody.innerHTML = '<tr><td colspan="6" class="text-center">No scientists assigned to your districts yet.</td></tr>';
                        return;
                    }

                    data.forEach(scientist => {
                        const districtsText = scientist.districts ? 
                            scientist.districts.map(d => `${d.state} - ${d.district}`).join(', ') : 
                            'No districts assigned';

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${scientist.name}</td>
                            <td>${scientist.email}</td>
                            <td>${scientist.phone_number || 'N/A'}</td>
                            <td>${scientist.designation || 'N/A'}</td>
                            <td>${districtsText}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="viewScientist(${scientist.id})">View Details</button>
                            </td>
                        `;
                        scientistsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading scientists:', error);
                    alert('Failed to load scientists');
                });
        }

        // View scientist function
        window.viewScientist = function(id) {
            fetch(`{{ route("district-state-coordinator.scientists.scientist", ["id" => ":id"]) }}`.replace(':id', id))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Populate modal with scientist data
                    document.getElementById('scientistName').textContent = data.name;
                    document.getElementById('scientistEmail').textContent = data.email;
                    document.getElementById('scientistPhone').textContent = data.phone_number || 'N/A';
                    document.getElementById('scientistDesignation').textContent = data.designation || 'N/A';

                    // Populate districts
                    const districtsDiv = document.getElementById('scientistDistricts');
                    if (data.districts && data.districts.length > 0) {
                        let districtsHtml = '<ul class="list-group">';
                        data.districts.forEach(district => {
                            districtsHtml += `
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    ${district.state} - ${district.district}
                                    <span class="badge bg-${district.status === 'active' ? 'success' : 'secondary'}">${district.status || 'Unknown'}</span>
                                </li>
                            `;
                        });
                        districtsHtml += '</ul>';
                        districtsDiv.innerHTML = districtsHtml;
                    } else {
                        districtsDiv.innerHTML = '<p class="text-muted">No districts assigned to this scientist.</p>';
                    }

                    scientistModal.show();
                })
                .catch(error => {
                    console.error('Error loading scientist details:', error);
                    alert('Failed to load scientist details');
                });
        };
    });
</script>
@endpush
