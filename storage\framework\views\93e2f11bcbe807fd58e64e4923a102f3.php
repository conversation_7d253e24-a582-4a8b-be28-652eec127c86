<?php $__env->startSection('title', 'Accepted Participation Requests'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Accepted Participation Requests</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($setupMessage)): ?>
                        <div class="alert alert-warning">
                            <p><strong>Profile Setup in Progress</strong></p>
                            <p><?php echo e($setupMessage); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p><strong>Instructions:</strong></p>
                            <ul>
                                <li>This page shows all participation requests you have accepted.</li>
                                <li>Make sure to mark these dates in your calendar and prepare for the events.</li>
                                <li>Contact the scientist directly if you need more information about the event.</li>
                            </ul>
                        </div>

                        <div class="table-responsive">
                        <table class="table table-striped" id="acceptedRequestsTable">
                            <thead>
                                <tr>
                                    <th>Scientist</th>
                                    <th>Action Plan</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>Request Message</th>
                                    <th>Your Response</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="acceptedRequestsTableBody">
                                <!-- Accepted requests will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div id="noAcceptedRequests" class="alert alert-info d-none">
                        No accepted participation requests found.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const acceptedRequestsTableBody = document.getElementById('acceptedRequestsTableBody');
        const noAcceptedRequests = document.getElementById('noAcceptedRequests');

        // Load accepted requests
        loadAcceptedRequests();

        function loadAcceptedRequests() {
            fetch('<?php echo e(route("district-state-coordinator.requests.accepted-requests")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }

                    if (data.length === 0) {
                        acceptedRequestsTableBody.innerHTML = '';
                        noAcceptedRequests.classList.remove('d-none');
                        return;
                    }

                    noAcceptedRequests.classList.add('d-none');
                    acceptedRequestsTableBody.innerHTML = '';

                    data.forEach(request => {
                        const row = document.createElement('tr');

                        // Format date
                        const date = new Date(request.action_plan.planned_date);
                        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        // Determine action plan status class
                        let statusClass = 'bg-primary';
                        if (request.action_plan.status === 'completed') {
                            statusClass = 'bg-success';
                        } else if (request.action_plan.status === 'cancelled') {
                            statusClass = 'bg-danger';
                        }

                        let rowHtml = `
                            <td>${request.scientist.name}<br><small>${request.scientist.email}</small></td>
                            <td>${request.action_plan.title}</td>
                            <td>${request.action_plan.type.replace('_', ' ').toUpperCase()}</td>
                            <td>${formattedDate}</td>
                            <td>${request.action_plan.location}</td>
                            <td>${request.request_message}</td>
                            <td>${request.response_message}</td>
                            <td><span class="badge ${statusClass}">${request.action_plan.status.toUpperCase()}</span></td>
                        `;

                        row.innerHTML = rowHtml;

                        // Add cancellation reason row if the action plan is cancelled
                        if (request.action_plan.status === 'cancelled' && request.action_plan.cancellation_reason) {
                            const reasonRow = document.createElement('tr');
                            reasonRow.className = 'cancellation-reason-row';
                            reasonRow.innerHTML = `
                                <td colspan="8" class="text-black" style="background-color: rgba(220, 53, 69, 0.2);">
                                    <strong>Reason for Cancellation:</strong> ${request.action_plan.cancellation_reason}
                                </td>
                            `;
                            acceptedRequestsTableBody.appendChild(reasonRow);
                        }

                        acceptedRequestsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while loading accepted requests');
                });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/requests/accepted.blade.php ENDPATH**/ ?>