<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\DistrictStateCoordinator;
use App\Models\StateData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DistrictController extends Controller
{
    /**
     * Display the districts page.
     */
    public function index()
    {
        return view('district-state-coordinator.districts.index');
    }

    /**
     * Get districts assigned to the coordinator.
     */
    public function getDistricts()
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();
            
            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }
            
            // Get districts assigned to this coordinator using the relationship
            $districts = $coordinator->districts()
                ->select('state_data.id', 'state_data.state', 'state_data.district', 'state_data.status', 'state_data.scientist')
                ->orderBy('state_data.state')
                ->orderBy('state_data.district')
                ->get();

            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error getting districts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get districts'], 500);
        }
    }

    /**
     * Get district details.
     */
    public function getDistrictDetails($id)
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();
            
            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }
            
            // Check if the district is assigned to this coordinator
            $isAssigned = DB::table('district_state_coordinator_mapping')
                ->where('district_state_coordinator_id', $coordinator->id)
                ->where('district_id', $id)
                ->exists();
            
            if (!$isAssigned) {
                return response()->json(['error' => 'District not assigned to you'], 403);
            }
            
            // Get the district details
            $district = StateData::where('id', $id)
                ->select('id', 'state', 'district', 'status', 'scientist')
                ->first();

            if (!$district) {
                return response()->json(['error' => 'District not found'], 404);
            }

            return response()->json($district);
        } catch (\Exception $e) {
            Log::error('Error getting district details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get district details'], 500);
        }
    }
}
