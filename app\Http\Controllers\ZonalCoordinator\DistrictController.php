<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ZonalCoordinator;
use App\Models\StateData;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DistrictController extends Controller
{
    /**
     * Display the districts page.
     */
    public function index()
    {
        return view('zonal-coordinator.districts.index');
    }

    /**
     * Get districts assigned to the coordinator.
     */
    public function getDistricts()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            $districts = StateData::whereIn('id', $districtIds)
                ->select('id', 'state', 'district', 'status', 'scientist')
                ->orderBy('state')
                ->orderBy('district')
                ->get();

            // Get all unique scientist emails from districts
            $scientistEmails = $districts->whereNotNull('scientist')
                ->pluck('scientist')
                ->unique()
                ->filter();

            // Fetch all scientist details in one query
            $scientists = collect();
            if ($scientistEmails->isNotEmpty()) {
                $scientists = User::whereIn('email', $scientistEmails)
                    ->where('role', 'scientist')
                    ->select('name', 'email', 'phone_number', 'designation')
                    ->get()
                    ->keyBy('email');

                // Log for debugging
                Log::info('Fetching scientist details', [
                    'emails_requested' => $scientistEmails->toArray(),
                    'scientists_found' => $scientists->keys()->toArray()
                ]);
            }

            // Attach scientist details to each district
            foreach ($districts as $district) {
                if ($district->scientist && $scientists->has($district->scientist)) {
                    $district->scientist_details = $scientists->get($district->scientist);
                } else {
                    $district->scientist_details = null;

                    // Log missing scientist details
                    if ($district->scientist) {
                        Log::warning('Scientist details not found', [
                            'email' => $district->scientist,
                            'district' => $district->district
                        ]);
                    }
                }
            }

            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error getting districts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get districts'], 500);
        }
    }

    /**
     * Debug scientist data - temporary method for troubleshooting
     */
    public function debugScientistData()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            $districts = StateData::whereIn('id', $districtIds)
                ->select('id', 'state', 'district', 'status', 'scientist')
                ->get();

            // Get all scientist emails
            $scientistEmails = $districts->whereNotNull('scientist')->pluck('scientist')->unique();

            // Check what users exist for these emails
            $allUsers = User::whereIn('email', $scientistEmails)->get(['name', 'email', 'role', 'phone_number']);
            $scientistUsers = User::whereIn('email', $scientistEmails)->where('role', 'scientist')->get(['name', 'email', 'role', 'phone_number']);

            return response()->json([
                'districts_count' => $districts->count(),
                'scientist_emails' => $scientistEmails->toArray(),
                'all_users_for_emails' => $allUsers->toArray(),
                'scientist_role_users' => $scientistUsers->toArray(),
                'sample_district' => $districts->first()
            ]);
        } catch (\Exception $e) {
            Log::error('Error in debug: ' . $e->getMessage());
            return response()->json(['error' => 'Debug failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get district details.
     */
    public function getDistrictDetails($id)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', auth()->id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Check if the district is assigned to this coordinator
            $isAssigned = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->where('district_id', $id)
                ->exists();

            if (!$isAssigned) {
                return response()->json(['error' => 'District not assigned to you'], 403);
            }

            // Get the district details
            $district = StateData::where('id', $id)
                ->select('id', 'state', 'district', 'status', 'scientist')
                ->first();

            if (!$district) {
                return response()->json(['error' => 'District not found'], 404);
            }

            return response()->json($district);
        } catch (\Exception $e) {
            Log::error('Error getting district details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get district details'], 500);
        }
    }
}
