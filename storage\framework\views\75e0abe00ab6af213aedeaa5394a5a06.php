<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('District Management')); ?></h5>
                    <a href="<?php echo e(route('super-admin.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Select a state and district to manage.</li>
                            <li>Enter the LGD (Local Government Directory) code for the district.</li>
                            <li>Set the district status (Pre-Cocoon or Post-Cocoon).</li>
                            <li>Assign a scientist to the district (one-to-one relationship).</li>
                        </ul>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="stateSelect">Select State:</label>
                                <select class="form-control" id="stateSelect">
                                    <option value="">-- Select State --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="districtSelect">Select District:</label>
                                <select class="form-control" id="districtSelect" disabled>
                                    <option value="">-- Select District --</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="districtUserForm" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 id="districtTitle">District Details</h5>
                            </div>
                            <div class="card-body">
                                <form id="saveDistrictUserForm">
                                    <input type="hidden" id="districtId" name="district_id">
                                    <input type="hidden" id="originalId" name="original_id">
                                    <input type="hidden" id="stateName" name="state_name">
                                    <input type="hidden" id="districtName" name="district_name">
                                    <input type="hidden" id="userId" name="user_id">
                                    <input type="hidden" id="isNew" name="is_new" value="false">

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="lgd_code">LGD Code:</label>
                                                <input type="text" class="form-control" id="lgd_code" name="lgd_code" placeholder="Enter Local Government Directory code">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="status">District Type:</label>
                                                <select class="form-control" id="status" name="status" required>
                                                    <option value="Pre-Cocoon">Pre-Cocoon</option>
                                                    <option value="Post-Cocoon">Post-Cocoon</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <h5 class="mt-4 mb-3">Scientist Information</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="name">Name:</label>
                                                <input type="text" class="form-control" id="name" name="name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="email">Email:</label>
                                                <input type="email" class="form-control" id="email" name="email" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="phone_number">Phone Number:</label>
                                                <input type="text" class="form-control" id="phone_number" name="phone_number" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="designation">Designation:</label>
                                                <input type="text" class="form-control" id="designation" name="designation" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="password">Password:</label>
                                                <div class="input-group">
                                                    <input type="password" class="form-control" id="password" name="password">
                                                    <button class="btn btn-outline-secondary" type="button" id="toggleSuperAdminPassword">
                                                        <i class="fas fa-eye" id="superAdminPasswordIcon"></i>
                                                    </button>
                                                </div>
                                                <small id="passwordHelpText" class="form-text text-muted">Leave blank to keep current password.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-primary">Save District & Scientist</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const stateSelect = document.getElementById('stateSelect');
        const districtSelect = document.getElementById('districtSelect');
        const districtUserForm = document.getElementById('districtUserForm');
        const saveDistrictUserForm = document.getElementById('saveDistrictUserForm');
        const passwordHelpText = document.getElementById('passwordHelpText');

        // Password toggle functionality
        const toggleSuperAdminPassword = document.getElementById('toggleSuperAdminPassword');
        if (toggleSuperAdminPassword) {
            toggleSuperAdminPassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const passwordIcon = document.getElementById('superAdminPasswordIcon');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    passwordIcon.classList.remove('fa-eye');
                    passwordIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    passwordIcon.classList.remove('fa-eye-slash');
                    passwordIcon.classList.add('fa-eye');
                }
            });
        }

        // Load states
        fetch('<?php echo e(route("super-admin.district-management.states")); ?>')
            .then(response => response.json())
            .then(data => {
                data.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state;
                    option.textContent = state;
                    stateSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading states:', error));

        // State selection change
        stateSelect.addEventListener('change', function() {
            districtSelect.innerHTML = '<option value="">-- Select District --</option>';
            districtSelect.disabled = !this.value;
            districtUserForm.style.display = 'none';

            if (this.value) {
                fetch(`<?php echo e(url("super-admin/district-management/districts")); ?>/${this.value}`)
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(district => {
                            const option = document.createElement('option');
                            option.value = district.id;
                            option.textContent = district.name;
                            option.dataset.status = district.status;
                            option.dataset.scientist = district.scientist;
                            option.dataset.originalId = district.original_id || district.id;
                            option.dataset.originalName = district.original_name || district.name;
                            districtSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error loading districts:', error));
            }
        });

        // District selection change
        districtSelect.addEventListener('change', function() {
            if (this.value) {
                fetch(`<?php echo e(url("super-admin/district-management/district")); ?>/${this.value}`)
                    .then(response => response.json())
                    .then(data => {
                        const district = data.district;
                        const scientist = data.scientist;

                        // Set form values
                        document.getElementById('districtId').value = district.id;
                        document.getElementById('originalId').value = district.original_id;
                        document.getElementById('stateName').value = district.state;
                        document.getElementById('districtName').value = district.district;
                        document.getElementById('lgd_code').value = district.lgd_code || '';
                        document.getElementById('status').value = district.status || 'Pre-Cocoon';
                        document.getElementById('districtTitle').textContent = district.district;

                        if (scientist) {
                            document.getElementById('name').value = scientist.name;
                            document.getElementById('email').value = scientist.email;
                            document.getElementById('phone_number').value = scientist.phone_number || '';
                            document.getElementById('designation').value = scientist.designation || '';
                            document.getElementById('userId').value = scientist.id || '';
                            document.getElementById('isNew').value = 'false';
                            passwordHelpText.style.display = 'block';
                        } else {
                            document.getElementById('name').value = '';
                            document.getElementById('email').value = '';
                            document.getElementById('phone_number').value = '';
                            document.getElementById('designation').value = '';
                            document.getElementById('userId').value = '';
                            document.getElementById('isNew').value = 'true';
                            passwordHelpText.style.display = 'none';
                        }

                        // Show the form
                        districtUserForm.style.display = 'block';
                    })
                    .catch(error => console.error('Error loading district details:', error));
            } else {
                districtUserForm.style.display = 'none';
            }
        });

        // Form submission
        saveDistrictUserForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {};
            formData.forEach((value, key) => data[key] = value);

            fetch('<?php echo e(route("super-admin.district-management.save")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    // Refresh the district list
                    stateSelect.dispatchEvent(new Event('change'));
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving district user:', error);
                alert('An error occurred while saving the district and scientist information.');
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\_csb-mrma.in (2)\csb\resources\views/super-admin/district-management/index.blade.php ENDPATH**/ ?>