<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class FormBuilderDebugController extends Controller
{
    public function index()
    {
        $forms = FormBuilder::all();
        return view('formBuilder.index', compact('forms'));
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'form_structure' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle form_structure - it might be a string (JSON) or already an array
        if (is_string($request->form_structure)) {
            $formStructure = json_decode($request->form_structure, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Invalid JSON in form_structure', ['error' => json_last_error_msg()]);
                return response()->json(['error' => 'Invalid form structure JSON'], 422);
            }
        } else {
            $formStructure = $request->form_structure;
        }

        // Validate fields
        if (!$this->validateFields($formStructure)) {
            Log::error('Invalid form structure fields', ['form_structure' => $formStructure]);
            return response()->json(['error' => 'Form structure must contain valid fields with type and label'], 422);
        }

        $form = FormBuilder::create([
            'form_name' => $request->form_name,
            'form_structure' => json_encode($formStructure),
        ]);

        Log::info('Form created successfully', [
            'form_id' => $form->id,
            'form_name' => $form->form_name,
            'field_count' => count($formStructure)
        ]);

        return response()->json(['message' => 'Form created successfully', 'form' => $form]);
    }

    public function editData($id)
    {
        $form = FormBuilder::findOrFail($id);
        return response()->json($form);
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'form_structure' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle form_structure - it might be a string (JSON) or already an array
        if (is_string($request->form_structure)) {
            $formStructure = json_decode($request->form_structure, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Invalid JSON in form_structure', ['error' => json_last_error_msg()]);
                return response()->json(['error' => 'Invalid form structure JSON'], 422);
            }
        } else {
            $formStructure = $request->form_structure;
        }

        // Validate fields
        if (!$this->validateFields($formStructure)) {
            Log::error('Invalid form structure fields', ['form_structure' => $formStructure]);
            return response()->json(['error' => 'Form structure must contain valid fields with type and label'], 422);
        }

        $form = FormBuilder::findOrFail($id);
        $form->update([
            'form_name' => $request->form_name,
            'form_structure' => json_encode($formStructure),
        ]);

        Log::info('Form updated successfully', [
            'form_id' => $form->id,
            'form_name' => $form->form_name,
            'field_count' => count($formStructure)
        ]);

        return response()->json(['message' => 'Form updated successfully', 'form' => $form]);
    }

    public function destroy($id)
    {
        $form = FormBuilder::findOrFail($id);
        $form->delete();
        Log::info('Form deleted', ['form_id' => $id]);
        return response()->json(['message' => 'Form deleted successfully']);
    }

    public function read()
    {
        $forms = FormBuilder::all();
        return response()->json($forms);
    }

    /**
     * Handle form submission and create dynamic table.
     */
    public function submitForm(Request $request)
    {
        try {
            Log::info('Form submission received', [
                'request_type' => $request->method(),
                'has_files' => $request->hasFile('*')
            ]);

            // Get the form ID
            $formId = $request->input('form_id') ?? $request->form_id;
            if (!$formId) {
                Log::error('Form ID missing');
                return response()->json(['error' => 'Form ID is required'], 422);
            }

            // Get the form details
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;

            // Parse form structure
            $formStructure = $form->form_structure;
            if (is_string($formStructure)) {
                $formStructure = json_decode($formStructure, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Failed to decode form structure JSON', [
                        'form_id' => $formId,
                        'error' => json_last_error_msg(),
                        'raw_structure' => substr($formStructure, 0, 200)
                    ]);
                    return response()->json(['error' => 'Invalid form structure format'], 422);
                }
            }

            if (!is_array($formStructure) || !$this->validateFields($formStructure)) {
                Log::error('Invalid form structure', [
                    'form_id' => $formId,
                    'type' => gettype($formStructure),
                    'form_structure' => $formStructure
                ]);
                return response()->json(['error' => 'Form structure must be an array with valid fields'], 422);
            }

            Log::info('Processed form structure', [
                'form_id' => $formId,
                'field_count' => count($formStructure),
                'first_field' => $formStructure[0] ?? 'none'
            ]);

            // Create a table name from the form name (sanitized)
            $tableName = 'form_' . Str::slug($formName, '_');

            // Build validation rules for file fields dynamically
            $validationRules = [];
            $fileFields = [];
            foreach ($formStructure as $index => $field) {
                if (isset($field['label'], $field['type']) && $field['type'] === 'file') {
                    $fieldName = $field['label'];
                    $rule = 'image|mimes:jpeg,png,jpg|max:2048';
                    if (isset($field['required']) && $field['required']) {
                        $rule = 'required|' . $rule;
                    }
                    $validationRules[$fieldName] = $rule;
                    $fileFields[$fieldName] = $index + 1;
                }
            }

            // Validate file fields
            if (!empty($validationRules)) {
                $validator = Validator::make($request->all(), $validationRules);
                if ($validator->fails()) {
                    Log::warning('File validation failed', ['errors' => $validator->errors()]);
                    return response()->json([
                        'error' => 'File validation failed: ' . implode(', ', array_map(fn($arr) => implode(', ', $arr), $validator->errors()))
                    ], 422);
                }
            }

            // Process form data
            $formData = [];
            $fileData = [];
            $hasFiles = $request->hasFile('*');

            if ($hasFiles) {
                Log::info('Processing multipart form with files');

                // Process non-file fields
                foreach ($request->all() as $key => $value) {
                    if ($key !== 'form_id' && $key !== '_token' && !$request->hasFile($key)) {
                        if ($value !== null && $value !== '') {
                            $formData[$key] = $value;
                        }
                    }
                }

                // Process file uploads using Laravel storage system

                foreach ($formStructure as $index => $field) {
                    if (isset($field['label'], $field['type']) && $field['type'] === 'file') {
                        $fieldName = $field['label'];
                        if ($request->hasFile($fieldName)) {
                            try {
                                $file = $request->file($fieldName);
                                Log::info("Processing file for field: {$fieldName}", [
                                    'original_name' => $file->getClientOriginalName(),
                                    'mime_type' => $file->getMimeType(),
                                    'size' => $file->getSize()
                                ]);

                                $fileName = time() . '_' . ($index + 1) . '_' . Str::slug($file->getClientOriginalName());

                                // Store file using Laravel storage system
                                $filePath = $file->storeAs('form_uploads', $fileName, 'public');

                                if ($filePath) {
                                    $formData[$fieldName] = $filePath;
                                    $fileData[$fieldName] = $filePath;
                                    Log::info('File uploaded successfully', [
                                        'field' => $fieldName,
                                        'path' => $filePath,
                                        'storage_path' => storage_path('app/public/' . $filePath),
                                        'url' => asset('storage/' . $filePath)
                                    ]);
                                } else {
                                    Log::error('File storage failed', [
                                        'field' => $fieldName
                                    ]);
                                    $formData[$fieldName] = null;
                                }
                            } catch (\Exception $e) {
                                Log::error('File upload failed', [
                                    'field' => $fieldName,
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString()
                                ]);
                                $formData[$fieldName] = null;
                            }
                        } else {
                            Log::warning('File field found but no file uploaded', ['field' => $fieldName]);
                            $formData[$fieldName] = null;
                        }
                    }
                }
            } else {
                Log::info('Processing JSON form data');
                if (!$request->has('form_data')) {
                    return response()->json(['error' => 'Form data is required'], 422);
                }

                $formData = $request->form_data;
                if (!is_array($formData)) {
                    Log::error('form_data is not an array', ['type' => gettype($formData)]);
                    return response()->json(['error' => 'Form data must be an array'], 422);
                }
            }

            // Create dynamic table if it doesn't exist
            if (!Schema::hasTable($tableName)) {
                Log::info("Creating dynamic table: {$tableName}");
                Schema::create($tableName, function ($table) use ($formStructure) {
                    $table->id();
                    $table->unsignedBigInteger('user_id');
                    foreach ($formStructure as $field) {
                        if (isset($field['label'])) {
                            $columnName = Str::slug($field['label'], '_');
                            switch ($field['type']) {
                                case 'textarea':
                                    $table->text($columnName)->nullable();
                                    break;
                                case 'number':
                                    $table->integer($columnName)->nullable();
                                    break;
                                case 'date':
                                    $table->date($columnName)->nullable();
                                    break;
                                case 'checkbox':
                                    $table->boolean($columnName)->default(false);
                                    break;
                                case 'file':
                                    $table->string($columnName, 255)->nullable();
                                    break;
                                case 'email':
                                    $table->string($columnName, 255)->nullable();
                                    break;
                                default:
                                    $table->string($columnName, 255)->nullable();
                                    break;
                            }
                        }
                    }
                    $table->timestamps();
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                });
                Log::info("Dynamic table {$tableName} created successfully");
            }

            // Log form data before submission
            Log::info('Final form data before submission', [
                'form_id' => $formId,
                'form_data' => $formData,
                'file_data' => $fileData
            ]);

            // Insert data into dynamic table
            $submissionId = FormSubmission::createSubmission($formId, Auth::id(), array_merge($formData, $fileData));
            if (!$submissionId) {
                throw new \Exception('Failed to create form submission');
            }

            Log::info("Form data inserted into dynamic table {$tableName}", ['submission_id' => $submissionId]);

            return response()->json([
                'message' => 'Form submitted successfully',
                'submission_id' => $submissionId
            ]);

        } catch (\Exception $e) {
            Log::error('Error submitting form: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to submit form: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Validate form fields structure
     */
    private function validateFields($fields)
    {
        if (!is_array($fields) || empty($fields)) {
            return false;
        }
        foreach ($fields as $field) {
            if (!is_array($field) || !isset($field['type']) || !isset($field['label'])) {
                return false;
            }
        }
        return true;
    }
}