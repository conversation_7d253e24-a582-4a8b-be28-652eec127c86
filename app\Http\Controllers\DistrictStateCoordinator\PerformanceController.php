<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\ScientistFeedback;
use App\Models\User;
use App\Models\DistrictStateCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceController extends Controller
{
    /**
     * Display the performance page.
     */
    public function index()
    {
        return view('district-state-coordinator.performance.index');
    }

    /**
     * Get performance data for the coordinator's districts.
     */
    public function getPerformanceData()
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', auth()->id())->first();
            
            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }
            
            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');
            
            // Get scientists in these districts
            $scientistEmails = DB::table('state_data')
                ->whereIn('id', $districtIds)
                ->whereNotNull('scientist')
                ->pluck('scientist')
                ->unique();
            
            $scientistIds = User::whereIn('email', $scientistEmails)
                ->where('role', 'scientist')
                ->pluck('id');
            
            // Get event statistics
            $eventStats = [
                'total' => Event::whereIn('district_id', $districtIds)->count(),
                'completed' => Event::whereIn('district_id', $districtIds)->where('status', 'completed')->count(),
                'planned' => Event::whereIn('district_id', $districtIds)->where('status', 'planned')->count(),
                'cancelled' => Event::whereIn('district_id', $districtIds)->where('status', 'cancelled')->count(),
            ];
            
            // Get participant statistics
            $participantStats = [
                'expected' => Event::whereIn('district_id', $districtIds)->sum('expected_participants'),
                'actual' => Event::whereIn('district_id', $districtIds)->sum('actual_participants'),
                'engagement_ratio' => $this->calculateEngagementRatio($districtIds),
            ];
            
            // Get scientist performance
            $scientistPerformance = $this->getScientistPerformance($scientistIds, $districtIds);
            
            // Get district performance
            $districtPerformance = $this->getDistrictPerformance($districtIds);
            
            // Get feedback statistics
            $feedbackStats = $this->getFeedbackStatistics($scientistIds);
            
            return response()->json([
                'eventStats' => $eventStats,
                'participantStats' => $participantStats,
                'scientistPerformance' => $scientistPerformance,
                'districtPerformance' => $districtPerformance,
                'feedbackStats' => $feedbackStats,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting performance data: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get performance data'], 500);
        }
    }

    /**
     * Calculate engagement ratio for the given districts.
     */
    private function calculateEngagementRatio($districtIds)
    {
        $expected = Event::whereIn('district_id', $districtIds)
            ->where('status', 'completed')
            ->sum('expected_participants');
        
        $actual = Event::whereIn('district_id', $districtIds)
            ->where('status', 'completed')
            ->sum('actual_participants');
        
        if ($expected == 0) {
            return 0;
        }
        
        return round(($actual / $expected) * 100, 2);
    }

    /**
     * Get scientist performance data.
     */
    private function getScientistPerformance($scientistIds, $districtIds)
    {
        $scientists = User::whereIn('id', $scientistIds)
            ->select('id', 'name', 'email')
            ->get();
        
        $performance = [];
        
        foreach ($scientists as $scientist) {
            $events = Event::where('scientist_id', $scientist->id)
                ->whereIn('district_id', $districtIds)
                ->get();
            
            $totalEvents = $events->count();
            $completedEvents = $events->where('status', 'completed')->count();
            $expectedParticipants = $events->sum('expected_participants');
            $actualParticipants = $events->sum('actual_participants');
            
            $feedback = ScientistFeedback::where('scientist_id', $scientist->id)
                ->select(
                    DB::raw('AVG(overall_rating) as avg_overall'),
                    DB::raw('AVG(preparedness_rating) as avg_preparedness'),
                    DB::raw('AVG(communication_rating) as avg_communication'),
                    DB::raw('AVG(engagement_rating) as avg_engagement'),
                    DB::raw('AVG(adherence_rating) as avg_adherence')
                )
                ->first();
            
            $performance[] = [
                'scientist' => $scientist,
                'events' => [
                    'total' => $totalEvents,
                    'completed' => $completedEvents,
                ],
                'participants' => [
                    'expected' => $expectedParticipants,
                    'actual' => $actualParticipants,
                    'ratio' => $expectedParticipants > 0 ? round(($actualParticipants / $expectedParticipants) * 100, 2) : 0,
                ],
                'feedback' => $feedback,
            ];
        }
        
        return $performance;
    }

    /**
     * Get district performance data.
     */
    private function getDistrictPerformance($districtIds)
    {
        $districts = DB::table('state_data')
            ->whereIn('id', $districtIds)
            ->select('id', 'state', 'district', 'status')
            ->get();
        
        $performance = [];
        
        foreach ($districts as $district) {
            $events = Event::where('district_id', $district->id)->get();
            
            $totalEvents = $events->count();
            $completedEvents = $events->where('status', 'completed')->count();
            $expectedParticipants = $events->sum('expected_participants');
            $actualParticipants = $events->sum('actual_participants');
            
            $performance[] = [
                'district' => $district,
                'events' => [
                    'total' => $totalEvents,
                    'completed' => $completedEvents,
                ],
                'participants' => [
                    'expected' => $expectedParticipants,
                    'actual' => $actualParticipants,
                    'ratio' => $expectedParticipants > 0 ? round(($actualParticipants / $expectedParticipants) * 100, 2) : 0,
                ],
            ];
        }
        
        return $performance;
    }

    /**
     * Get feedback statistics.
     */
    private function getFeedbackStatistics($scientistIds)
    {
        $feedback = ScientistFeedback::whereIn('scientist_id', $scientistIds)
            ->select(
                DB::raw('AVG(overall_rating) as avg_overall'),
                DB::raw('AVG(preparedness_rating) as avg_preparedness'),
                DB::raw('AVG(communication_rating) as avg_communication'),
                DB::raw('AVG(engagement_rating) as avg_engagement'),
                DB::raw('AVG(adherence_rating) as avg_adherence'),
                DB::raw('COUNT(*) as total_feedback')
            )
            ->first();
        
        return $feedback;
    }
}
