<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><?php echo e(__('SWOT Analysis')); ?></span>
                        <a href="<?php echo e(route('scientist.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <?php if(session('success')): ?>
                    <div class="alert alert-success">
                        <?php echo e(session('success')); ?>

                    </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                    <div class="alert alert-danger">
                        <?php echo e(session('error')); ?>

                    </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Create a SWOT (Strengths, Weaknesses, Opportunities, Threats) analysis for your district.</li>
                            <li>Each section has a maximum character limit of 1000 characters.</li>
                            <li>Try to identify at least 4 points for each section.</li>
                            <li>Your SWOT analysis will help in strategic planning for your district.</li>
                        </ul>
                    </div>

                    <div id="districtInfo" class="mb-4">
                        <!-- District information will be displayed here -->
                    </div>

                    <form id="swotForm">
                        <input type="hidden" id="districtId" name="district_id">

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-success text-white">
                                        <strong>Strengths</strong>
                                        <small class="float-end"><span id="strengthsCount">0</span>/1000</small>
                                    </div>
                                    <div class="card-body">
                                        <textarea class="form-control" id="strengths" name="strengths" rows="8" maxlength="1000" placeholder="List the strengths of sericulture in your district..."></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-danger text-white">
                                        <strong>Weaknesses</strong>
                                        <small class="float-end"><span id="weaknessesCount">0</span>/1000</small>
                                    </div>
                                    <div class="card-body">
                                        <textarea class="form-control" id="weaknesses" name="weaknesses" rows="8" maxlength="1000" placeholder="List the weaknesses or challenges in your district..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-primary text-white">
                                        <strong>Opportunities</strong>
                                        <small class="float-end"><span id="opportunitiesCount">0</span>/1000</small>
                                    </div>
                                    <div class="card-body">
                                        <textarea class="form-control" id="opportunities" name="opportunities" rows="8" maxlength="1000" placeholder="List the opportunities for growth and development..."></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-warning text-dark">
                                        <strong>Threats</strong>
                                        <small class="float-end"><span id="threatsCount">0</span>/1000</small>
                                    </div>
                                    <div class="card-body">
                                        <textarea class="form-control" id="threats" name="threats" rows="8" maxlength="1000" placeholder="List the threats or risks to sericulture in your district..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <button type="submit" id="saveButton" class="btn btn-primary">Save SWOT Analysis</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const swotForm = document.getElementById('swotForm');
        const saveButton = document.getElementById('saveButton');
        const districtInfo = document.getElementById('districtInfo');
        const districtIdInput = document.getElementById('districtId');

        // Character counters
        const strengths = document.getElementById('strengths');
        const weaknesses = document.getElementById('weaknesses');
        const opportunities = document.getElementById('opportunities');
        const threats = document.getElementById('threats');

        const strengthsCount = document.getElementById('strengthsCount');
        const weaknessesCount = document.getElementById('weaknessesCount');
        const opportunitiesCount = document.getElementById('opportunitiesCount');
        const threatsCount = document.getElementById('threatsCount');

        // Update character counts
        function updateCharCount(textarea, counter) {
            counter.textContent = textarea.value.length;
        }

        strengths.addEventListener('input', () => updateCharCount(strengths, strengthsCount));
        weaknesses.addEventListener('input', () => updateCharCount(weaknesses, weaknessesCount));
        opportunities.addEventListener('input', () => updateCharCount(opportunities, opportunitiesCount));
        threats.addEventListener('input', () => updateCharCount(threats, threatsCount));

        // Load SWOT analysis data
        function loadSwotAnalysis() {
            // Show loading indicator
            districtInfo.innerHTML = '<div class="alert alert-info">Loading district information...</div>';

            fetch('<?php echo e(route("scientist.swot.get")); ?>')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        districtInfo.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
                        swotForm.style.display = 'none';
                        return;
                    }

                    // Display district info
                    const district = data.district;
                    const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';

                    districtInfo.innerHTML = `
                        <div class="alert alert-secondary">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>District:</strong> ${district.district}, ${district.state}
                                    <span class="badge ${badgeClass} ms-2">${district.status}</span>
                                </div>
                            </div>
                        </div>
                    `;

                    // Set district ID
                    districtIdInput.value = district.id;

                    // If SWOT analysis exists, populate the form and hide save button
                    if (data.swot) {
                        strengths.value = data.swot.strengths || '';
                        weaknesses.value = data.swot.weaknesses || '';
                        opportunities.value = data.swot.opportunities || '';
                        threats.value = data.swot.threats || '';

                        // Update character counts
                        updateCharCount(strengths, strengthsCount);
                        updateCharCount(weaknesses, weaknessesCount);
                        updateCharCount(opportunities, opportunitiesCount);
                        updateCharCount(threats, threatsCount);

                        // Hide save button and show completion message
                        saveButton.style.display = 'none';

                        // Add completion status to district info
                        districtInfo.innerHTML = `
                            <div class="alert alert-secondary">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>District:</strong> ${district.district}, ${district.state}
                                        <span class="badge ${badgeClass} ms-2">${district.status}</span>
                                    </div>
                                    <div>
                                        <span class="badge bg-success">SWOT Analysis Completed</span>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Make form fields readonly
                        strengths.readOnly = true;
                        weaknesses.readOnly = true;
                        opportunities.readOnly = true;
                        threats.readOnly = true;
                    } else {
                        // Show save button and make fields editable for new SWOT
                        saveButton.style.display = 'block';
                        saveButton.disabled = false;
                        saveButton.innerHTML = 'Save SWOT Analysis';

                        // Make form fields editable
                        strengths.readOnly = false;
                        weaknesses.readOnly = false;
                        opportunities.readOnly = false;
                        threats.readOnly = false;
                    }
                })
                .catch(error => {
                    console.error('Error loading SWOT analysis:', error);
                    districtInfo.innerHTML = '<div class="alert alert-danger">Error loading SWOT analysis. Please try again later.</div>';
                });
        }

        // Save SWOT analysis
        swotForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!swotForm.checkValidity()) {
                swotForm.reportValidity();
                return;
            }

            // Disable save button
            saveButton.disabled = true;
            saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

            // Create form data
            const formData = new FormData();
            formData.append('strengths', strengths.value);
            formData.append('weaknesses', weaknesses.value);
            formData.append('opportunities', opportunities.value);
            formData.append('threats', threats.value);
            formData.append('_token', '<?php echo e(csrf_token()); ?>');

            console.log('Submitting SWOT data:', {
                strengths: strengths.value,
                weaknesses: weaknesses.value,
                opportunities: opportunities.value,
                threats: threats.value
            });

            // Send the data
            fetch('<?php echo e(route("scientist.swot.save")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    // Re-enable save button on error
                    saveButton.disabled = false;
                    saveButton.innerHTML = 'Save SWOT Analysis';
                } else {
                    alert(data.message);
                    // Reload the SWOT data to update the UI
                    loadSwotAnalysis();
                }
            })
            .catch(error => {
                console.error('Error saving SWOT analysis:', error);
                alert('Error saving SWOT analysis. Please try again later.');

                // Re-enable save button
                saveButton.disabled = false;
                saveButton.innerHTML = 'Save SWOT Analysis';
            });
        });

        // Load SWOT analysis on page load
        loadSwotAnalysis();
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/scientist/swot/new_index.blade.php ENDPATH**/ ?>