@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Performance Dashboard') }}</h5>
                    <a href="{{ route('district-state-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Monitor performance metrics for scientists and districts under your coordination.</li>
                            <li>Track event completion rates, participant engagement, and feedback scores.</li>
                            <li>Use this data to identify areas for improvement and provide guidance.</li>
                        </ul>
                    </div>

                    <!-- Performance Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Events</h5>
                                    <h2 id="totalEvents">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Completed Events</h5>
                                    <h2 id="completedEvents">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Total Participants</h5>
                                    <h2 id="totalParticipants">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">Engagement Rate</h5>
                                    <h2 id="engagementRate">-</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Tables -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Scientist Performance</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Scientist</th>
                                                    <th>Events</th>
                                                    <th>Completion Rate</th>
                                                    <th>Avg Rating</th>
                                                </tr>
                                            </thead>
                                            <tbody id="scientistPerformanceBody">
                                                <!-- Scientist performance data will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">District Performance</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>District</th>
                                                    <th>Events</th>
                                                    <th>Participants</th>
                                                    <th>Engagement</th>
                                                </tr>
                                            </thead>
                                            <tbody id="districtPerformanceBody">
                                                <!-- District performance data will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Statistics -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Feedback Statistics</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="avgOverallRating">-</h5>
                                                <small>Overall Rating</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="avgPreparedness">-</h5>
                                                <small>Preparedness</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="avgCommunication">-</h5>
                                                <small>Communication</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="avgEngagement">-</h5>
                                                <small>Engagement</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="avgAdherence">-</h5>
                                                <small>Adherence</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <h5 id="totalFeedback">-</h5>
                                                <small>Total Feedback</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load performance data
        loadPerformanceData();

        function loadPerformanceData() {
            fetch('{{ route("district-state-coordinator.performance.get-performance-data") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Update summary cards
                    document.getElementById('totalEvents').textContent = data.eventStats.total || 0;
                    document.getElementById('completedEvents').textContent = data.eventStats.completed || 0;
                    document.getElementById('totalParticipants').textContent = data.participantStats.actual || 0;
                    document.getElementById('engagementRate').textContent = (data.participantStats.engagement_ratio || 0) + '%';

                    // Update scientist performance table
                    const scientistBody = document.getElementById('scientistPerformanceBody');
                    scientistBody.innerHTML = '';
                    
                    if (data.scientistPerformance && data.scientistPerformance.length > 0) {
                        data.scientistPerformance.forEach(scientist => {
                            const completionRate = scientist.events.total > 0 ? 
                                Math.round((scientist.events.completed / scientist.events.total) * 100) : 0;
                            const avgRating = scientist.feedback && scientist.feedback.avg_overall ? 
                                parseFloat(scientist.feedback.avg_overall).toFixed(1) : 'N/A';

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${scientist.scientist.name}</td>
                                <td>${scientist.events.total}</td>
                                <td>${completionRate}%</td>
                                <td>${avgRating}</td>
                            `;
                            scientistBody.appendChild(row);
                        });
                    } else {
                        scientistBody.innerHTML = '<tr><td colspan="4" class="text-center">No scientist data available</td></tr>';
                    }

                    // Update district performance table
                    const districtBody = document.getElementById('districtPerformanceBody');
                    districtBody.innerHTML = '';
                    
                    if (data.districtPerformance && data.districtPerformance.length > 0) {
                        data.districtPerformance.forEach(district => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${district.district.state} - ${district.district.district}</td>
                                <td>${district.events.total}</td>
                                <td>${district.participants.actual}/${district.participants.expected}</td>
                                <td>${district.participants.ratio}%</td>
                            `;
                            districtBody.appendChild(row);
                        });
                    } else {
                        districtBody.innerHTML = '<tr><td colspan="4" class="text-center">No district data available</td></tr>';
                    }

                    // Update feedback statistics
                    if (data.feedbackStats) {
                        document.getElementById('avgOverallRating').textContent = 
                            data.feedbackStats.avg_overall ? parseFloat(data.feedbackStats.avg_overall).toFixed(1) : 'N/A';
                        document.getElementById('avgPreparedness').textContent = 
                            data.feedbackStats.avg_preparedness ? parseFloat(data.feedbackStats.avg_preparedness).toFixed(1) : 'N/A';
                        document.getElementById('avgCommunication').textContent = 
                            data.feedbackStats.avg_communication ? parseFloat(data.feedbackStats.avg_communication).toFixed(1) : 'N/A';
                        document.getElementById('avgEngagement').textContent = 
                            data.feedbackStats.avg_engagement ? parseFloat(data.feedbackStats.avg_engagement).toFixed(1) : 'N/A';
                        document.getElementById('avgAdherence').textContent = 
                            data.feedbackStats.avg_adherence ? parseFloat(data.feedbackStats.avg_adherence).toFixed(1) : 'N/A';
                        document.getElementById('totalFeedback').textContent = data.feedbackStats.total_feedback || 0;
                    }
                })
                .catch(error => {
                    console.error('Error loading performance data:', error);
                    alert('Failed to load performance data');
                });
        }
    });
</script>
@endpush
