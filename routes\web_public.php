<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicFeedbackController;
use App\Http\Controllers\PublicPageController;

// Public page routes (accessible to all users)
Route::middleware('guest')->group(function () {
    Route::get('/', [PublicPageController::class, 'home'])->name('public.home');
    Route::get('/about-us', [PublicPageController::class, 'aboutUs'])->name('public.about-us');
    Route::get('/who-is-who', [PublicPageController::class, 'whoIsWho'])->name('public.who-is-who');
    Route::get('/upcoming-programs', [PublicPageController::class, 'upcomingPrograms'])->name('public.upcoming-programs');
    Route::get('/contact-us', [PublicPageController::class, 'contactUs'])->name('public.contact-us');
    Route::post('/contact-us', [PublicPageController::class, 'submitContact'])->middleware('secure.upload')->name('public.contact-us.submit');
});

// Public routes that don't require authentication
Route::prefix('public')->name('public.')->group(function () {
    // Feedback routes
    Route::prefix('feedback')->name('feedback.')->group(function () {
        Route::get('/{eventId}', [PublicFeedbackController::class, 'showFeedbackForm'])->name('form');
        Route::post('/{eventId}', [PublicFeedbackController::class, 'submitFeedback'])->middleware('secure.upload')->name('submit');
        Route::get('/{eventId}/qr', [PublicFeedbackController::class, 'showQrCode'])->name('qr');

        // Action Plan feedback routes
        Route::get('/action-plan/{actionPlanId}', [PublicFeedbackController::class, 'showActionPlanFeedbackForm'])->name('actionplan.form');
        Route::post('/action-plan/{actionPlanId}', [PublicFeedbackController::class, 'submitActionPlanFeedback'])->middleware('secure.upload')->name('actionplan.submit');
        Route::get('/action-plan/{actionPlanId}/qr', [PublicFeedbackController::class, 'showActionPlanQrCode'])->name('actionplan.qr');
    });
});
