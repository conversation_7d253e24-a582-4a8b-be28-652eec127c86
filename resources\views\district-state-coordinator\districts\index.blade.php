@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('My Districts') }}</h5>
                    <a href="{{ route('district-state-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all districts assigned to you as a District State Coordinator.</li>
                            <li>Click on a district to view its details and assigned scientist.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>District</th>
                                    <th>State</th>
                                    <th>Type</th>
                                    <th>Assigned Scientist</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="districtsTableBody">
                                <!-- Districts will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- District Details Modal -->
                    <div class="modal fade" id="districtModal" tabindex="-1" aria-labelledby="districtModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="districtModalLabel">District Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>District Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>District Name</th>
                                                    <td id="districtName"></td>
                                                </tr>
                                                <tr>
                                                    <th>State</th>
                                                    <td id="stateName"></td>
                                                </tr>
                                                <tr>
                                                    <th>Type</th>
                                                    <td id="districtType"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Assigned Scientist</h6>
                                            <div id="scientistInfo">
                                                <!-- Scientist info will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const districtsTableBody = document.getElementById('districtsTableBody');
        const districtModal = new bootstrap.Modal(document.getElementById('districtModal'));

        // Load districts
        loadDistricts();

        // Load districts function
        function loadDistricts() {
            fetch('{{ route("district-state-coordinator.districts.get-districts") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    districtsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        districtsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No districts assigned to you yet.</td></tr>';
                        return;
                    }

                    data.forEach(district => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${district.district}</td>
                            <td>${district.state}</td>
                            <td><span class="badge bg-${district.status === 'active' ? 'success' : 'secondary'}">${district.status || 'Unknown'}</span></td>
                            <td>${district.scientist || 'Not assigned'}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="viewDistrict(${district.id})">View Details</button>
                            </td>
                        `;
                        districtsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading districts:', error);
                    alert('Failed to load districts');
                });
        }

        // View district function
        window.viewDistrict = function(id) {
            fetch(`{{ route("district-state-coordinator.districts.district", ["id" => ":id"]) }}`.replace(':id', id))
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Populate modal with district data
                    document.getElementById('districtName').textContent = data.district;
                    document.getElementById('stateName').textContent = data.state;
                    document.getElementById('districtType').innerHTML = `<span class="badge bg-${data.status === 'active' ? 'success' : 'secondary'}">${data.status || 'Unknown'}</span>`;

                    // Populate scientist info
                    const scientistInfo = document.getElementById('scientistInfo');
                    if (data.scientist) {
                        scientistInfo.innerHTML = `
                            <p><strong>Email:</strong> ${data.scientist}</p>
                            <p><em>Contact the scientist for more details about activities in this district.</em></p>
                        `;
                    } else {
                        scientistInfo.innerHTML = '<p class="text-muted">No scientist assigned to this district yet.</p>';
                    }

                    districtModal.show();
                })
                .catch(error => {
                    console.error('Error loading district details:', error);
                    alert('Failed to load district details');
                });
        };
    });
</script>
@endpush
