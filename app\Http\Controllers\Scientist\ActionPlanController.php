<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\ScientistFeedback;
use App\Models\StateData;
use App\Models\DistrictStateCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Auth;

class ActionPlanController extends Controller
{
    /**
     * Display the action plans page.
     */
    public function index()
    {
        return view('scientist.action-plans.index');
    }

    /**
     * Get the scientist's district.
     * If no district is assigned, create a default one.
     */
    private function getScientistDistrict()
    {
        $scientistEmail = Auth::user()->email;
        $district = StateData::where('scientist', $scientistEmail)->first();

        // If no district is assigned, create a default one
        if (!$district) {
            Log::warning('No district found for scientist, creating default district', [
                'scientist_email' => $scientistEmail
            ]);

            try {
                // Create a default district for the scientist
                $district = StateData::create([
                    'state' => 'Default State',
                    'district' => 'Default District',
                    'state_name' => 'Default State',
                    'district_name' => 'Default District',
                    'scientist' => $scientistEmail,
                    'status' => 'pre-cocoon', // Default status
                ]);

                Log::info('Default district created for scientist', [
                    'district_id' => $district->id,
                    'scientist_email' => $scientistEmail
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to create default district for scientist', [
                    'scientist_email' => $scientistEmail,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $district;
    }

    /**
     * Get all action plans for the current scientist.
     */
    public function getActionPlans()
    {
        try {
            // Log the scientist ID for debugging
            $scientistId = Auth::id();
            $scientistEmail = Auth::user()->email;

            Log::info('Getting action plans for scientist', [
                'scientist_id' => $scientistId,
                'scientist_email' => $scientistEmail
            ]);

            // Check if the scientist has a district assigned
            $district = $this->getScientistDistrict();
            Log::info('Scientist district check', [
                'has_district' => $district ? true : false,
                'district_id' => $district ? $district->id : null,
                'district_name' => $district ? $district->district_name : null
            ]);

            $actionPlans = ActionPlan::where('scientist_id', $scientistId)
                ->orderBy('planned_date', 'desc')
                ->get();

            // Enhance each action plan with feedback data if available
            $actionPlans->each(function ($actionPlan) {
                $feedback = \App\Models\ActionPlanFeedback::where('action_plan_id', $actionPlan->id)->first();

                if ($feedback) {
                    // Override action plan participant data with feedback data (actual submitted data)
                    $actionPlan->male_participants = $feedback->male_participants;
                    $actionPlan->female_participants = $feedback->female_participants;
                    $actionPlan->transgender_participants = $feedback->transgender_participants;
                    $actionPlan->st_participants = $feedback->st_participants;
                    $actionPlan->sc_participants = $feedback->sc_participants;
                    $actionPlan->general_participants = $feedback->general_participants;
                    $actionPlan->obc_participants = $feedback->obc_participants;
                    $actionPlan->photo1 = $feedback->photo1;
                    $actionPlan->photo2 = $feedback->photo2;
                    $actionPlan->photo3 = $feedback->photo3;
                    $actionPlan->photo4 = $feedback->photo4;
                    $actionPlan->gis_location = $feedback->gis_location;
                    $actionPlan->scientist_event_success_rating = $feedback->event_success_rating;
                    $actionPlan->scientist_challenges_faced = $feedback->challenges_faced;
                    $actionPlan->scientist_suggestions_for_improvement = $feedback->suggestions_for_improvement;
                    $actionPlan->scientist_objectives_met = $feedback->objectives_met;
                    $actionPlan->scientist_self_assessed_learning_outcome = $feedback->self_assessed_learning_outcome;
                }
            });

            Log::info('Action plans retrieved', [
                'count' => $actionPlans->count(),
                'plans' => $actionPlans->toArray()
            ]);

            return response()->json($actionPlans);
        } catch (\Exception $e) {
            Log::error('Error getting action plans: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get action plans'], 500);
        }
    }

    /**
     * Get a specific action plan.
     */
    public function getActionPlan($id)
    {
        try {
            $actionPlan = ActionPlan::where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Get feedback data from actionplan_feedback table if available
            $feedback = \App\Models\ActionPlanFeedback::where('action_plan_id', $id)->first();

            if ($feedback) {
                // Override action plan participant data with feedback data (actual submitted data)
                $actionPlan->male_participants = $feedback->male_participants;
                $actionPlan->female_participants = $feedback->female_participants;
                $actionPlan->transgender_participants = $feedback->transgender_participants;
                $actionPlan->st_participants = $feedback->st_participants;
                $actionPlan->sc_participants = $feedback->sc_participants;
                $actionPlan->general_participants = $feedback->general_participants;
                $actionPlan->obc_participants = $feedback->obc_participants;
                $actionPlan->photo1 = $feedback->photo1;
                $actionPlan->photo2 = $feedback->photo2;
                $actionPlan->photo3 = $feedback->photo3;
                $actionPlan->photo4 = $feedback->photo4;
                $actionPlan->gis_location = $feedback->gis_location;
                $actionPlan->scientist_event_success_rating = $feedback->event_success_rating;
                $actionPlan->scientist_challenges_faced = $feedback->challenges_faced;
                $actionPlan->scientist_suggestions_for_improvement = $feedback->suggestions_for_improvement;
                $actionPlan->scientist_objectives_met = $feedback->objectives_met;
                $actionPlan->scientist_self_assessed_learning_outcome = $feedback->self_assessed_learning_outcome;

                // Add VIP participation fields
                $actionPlan->vip_participated = $feedback->vip_participated;
                $actionPlan->vip_mla_mp = $feedback->vip_mla_mp;
                $actionPlan->vip_ms_jst_director = $feedback->vip_ms_jst_director;
                $actionPlan->vip_local_pr = $feedback->vip_local_pr;
                $actionPlan->vip_state_official = $feedback->vip_state_official;
                $actionPlan->vip_others = $feedback->vip_others;
                $actionPlan->vip_others_details = $feedback->vip_others_details;

                // Add media coverage fields
                $actionPlan->media_coverage = $feedback->media_coverage;
                $actionPlan->media_newspaper = $feedback->media_newspaper;
                $actionPlan->media_tv_news = $feedback->media_tv_news;
                $actionPlan->media_radio = $feedback->media_radio;
                $actionPlan->media_link = $feedback->media_link;
                $actionPlan->media_photo = $feedback->media_photo;

                // Add social media link
                $actionPlan->social_media_link = $feedback->social_media_link;

                Log::info('Action plan data enhanced with feedback data', [
                    'action_plan_id' => $id,
                    'feedback_id' => $feedback->id,
                    'media_links' => $feedback->media_links,
                    'social_media_links' => $feedback->social_media_links,
                    'media_links_type' => gettype($feedback->media_links),
                    'social_media_links_type' => gettype($feedback->social_media_links)
                ]);
            }

            // Get participation requests for this action plan
            if (Schema::hasTable('participation_requests')) {
                $participationRequests = \App\Models\ParticipationRequest::where('action_plan_id', $id)
                    ->with(['coordinator:id,name,email,designation'])
                    ->get();

                $actionPlan->participation_requests = $participationRequests;

                Log::info('Participation requests retrieved for action plan', [
                    'action_plan_id' => $id,
                    'count' => $participationRequests->count()
                ]);
            }

            return response()->json($actionPlan);
        } catch (\Exception $e) {
            Log::error('Error getting action plan: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get action plan'], 500);
        }
    }

    /**
     * Get available district state coordinators.
     */
    public function getCoordinators()
    {
        try {
            // Check if the district_state_coordinators table exists
            if (!Schema::hasTable('district_state_coordinators')) {
                Log::warning('District state coordinators table does not exist');
                return response()->json([]);
            }

            Log::info('Getting coordinators for scientist', [
                'scientist_id' => Auth::id(),
                'scientist_email' => Auth::user()->email
            ]);

            try {
                // Get all available DSCs in the system
                $coordinators = DistrictStateCoordinator::select('id', 'name', 'designation', 'email', 'phone_number', 'expertise_areas')
                    ->get();

                Log::info('Available coordinators found', [
                    'coordinator_count' => $coordinators->count(),
                    'coordinators' => $coordinators->toArray()
                ]);

                return response()->json($coordinators);
            } catch (\Exception $e) {
                // If there's an error, log it and return an empty array
                Log::error('Error getting coordinators: ' . $e->getMessage(), [
                    'trace' => $e->getTraceAsString()
                ]);
                return response()->json([]);
            }
        } catch (\Exception $e) {
            Log::error('Error getting coordinators: ' . $e->getMessage(), [
                'scientist_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get coordinators'], 500);
        }
    }

    /**
     * Create a new action plan.
     */
    public function createActionPlan(Request $request)
    {
        $validationRules = [
            'type' => 'required|in:training,field_visit,demonstration,awareness',
            'title' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'planned_date' => 'required|date|after:today',
            'expected_participants' => 'required|integer|min:1',
            'required_coordinators' => 'nullable|array',
        ];

        // Only add the exists validation if the table exists
        if (Schema::hasTable('district_state_coordinators')) {
            $validationRules['required_coordinators.*'] = 'exists:district_state_coordinators,id';
        }

        $request->validate($validationRules);

        try {
            $district = $this->getScientistDistrict();

            if (!$district) {
                return response()->json(['error' => 'No district assigned to this scientist'], 404);
            }

            $actionPlan = ActionPlan::create([
                'scientist_id' => Auth::id(),
                'district_id' => $district->id,
                'type' => $request->type,
                'title' => $request->title,
                'location' => $request->location,
                'planned_date' => $request->planned_date,
                'expected_participants' => $request->expected_participants,
                'required_coordinators' => $request->required_coordinators,
                'status' => 'planned',
            ]);

            // Create participation requests for each coordinator
            if ($request->required_coordinators && is_array($request->required_coordinators)) {
                foreach ($request->required_coordinators as $coordinatorId) {
                    // Get the coordinator's user_id
                    $coordinator = DistrictStateCoordinator::find($coordinatorId);

                    if ($coordinator && $coordinator->user_id) {
                        try {
                            // Check if the participation_requests table exists
                            if (Schema::hasTable('participation_requests')) {
                                // Create a participation request with custom message if provided
                                $requestMessage = $request->invitation_message
                                    ? $request->invitation_message
                                    : "I would like to invite you to participate in my action plan: {$request->title}";

                                \App\Models\ParticipationRequest::create([
                                    'scientist_id' => Auth::id(),
                                    'coordinator_id' => $coordinator->user_id,
                                    'action_plan_id' => $actionPlan->id,
                                    'status' => 'pending',
                                    'request_message' => $requestMessage,
                                ]);

                                Log::info('Created participation request for coordinator', [
                                    'scientist_id' => Auth::id(),
                                    'coordinator_id' => $coordinator->user_id,
                                    'action_plan_id' => $actionPlan->id,
                                    'coordinator_name' => $coordinator->name
                                ]);
                            } else {
                                Log::warning('Participation requests table does not exist yet. Skipping request creation.');
                            }
                        } catch (\Exception $e) {
                            Log::error('Failed to create participation request', [
                                'error' => $e->getMessage(),
                                'coordinator_id' => $coordinatorId,
                                'action_plan_id' => $actionPlan->id
                            ]);
                        }
                    } else {
                        Log::warning('Coordinator not found or has no user_id', [
                            'coordinator_id' => $coordinatorId
                        ]);
                    }
                }
            }

            return response()->json([
                'message' => 'Action plan created successfully',
                'action_plan' => $actionPlan
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating action plan: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create action plan'], 500);
        }
    }

    /**
     * Update an action plan.
     */
    public function updateActionPlan(Request $request, $id)
    {
        $validationRules = [
            'type' => 'required|in:training,field_visit,demonstration,awareness',
            'title' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'planned_date' => 'required|date',
            'expected_participants' => 'required|integer|min:1',
            'required_coordinators' => 'nullable|array',
        ];

        // Only add the exists validation if the table exists
        if (Schema::hasTable('district_state_coordinators')) {
            $validationRules['required_coordinators.*'] = 'exists:district_state_coordinators,id';
        }

        $request->validate($validationRules);

        try {
            $actionPlan = ActionPlan::where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Only allow updates if the action plan is still in 'planned' status
            if ($actionPlan->status !== 'planned') {
                return response()->json(['error' => 'Cannot update a completed or cancelled action plan'], 403);
            }

            // Get the old required coordinators before updating
            $oldCoordinators = $actionPlan->required_coordinators ?? [];

            $actionPlan->update([
                'type' => $request->type,
                'title' => $request->title,
                'location' => $request->location,
                'planned_date' => $request->planned_date,
                'expected_participants' => $request->expected_participants,
                'required_coordinators' => $request->required_coordinators,
            ]);

            // Handle participation requests for coordinators
            if ($request->required_coordinators && is_array($request->required_coordinators)) {
                // Find new coordinators that weren't in the old list
                $newCoordinators = array_diff($request->required_coordinators, $oldCoordinators);

                foreach ($newCoordinators as $coordinatorId) {
                    // Get the coordinator's user_id
                    $coordinator = DistrictStateCoordinator::find($coordinatorId);

                    if ($coordinator && $coordinator->user_id) {
                        try {
                            // Check if the participation_requests table exists
                            if (Schema::hasTable('participation_requests')) {
                                // Check if a request already exists
                                $existingRequest = \App\Models\ParticipationRequest::where('scientist_id', Auth::id())
                                    ->where('coordinator_id', $coordinator->user_id)
                                    ->where('action_plan_id', $actionPlan->id)
                                    ->first();

                                if (!$existingRequest) {
                                    // Create a participation request with custom message if provided
                                    $requestMessage = $request->invitation_message
                                        ? $request->invitation_message
                                        : "I would like to invite you to participate in my action plan: {$request->title}";

                                    \App\Models\ParticipationRequest::create([
                                        'scientist_id' => Auth::id(),
                                        'coordinator_id' => $coordinator->user_id,
                                        'action_plan_id' => $actionPlan->id,
                                        'status' => 'pending',
                                        'request_message' => $requestMessage,
                                    ]);

                                    Log::info('Created participation request for coordinator during update', [
                                        'scientist_id' => Auth::id(),
                                        'coordinator_id' => $coordinator->user_id,
                                        'action_plan_id' => $actionPlan->id,
                                        'coordinator_name' => $coordinator->name
                                    ]);
                                }
                            } else {
                                Log::warning('Participation requests table does not exist yet. Skipping request creation.');
                            }
                        } catch (\Exception $e) {
                            Log::error('Failed to create participation request during update', [
                                'error' => $e->getMessage(),
                                'coordinator_id' => $coordinatorId,
                                'action_plan_id' => $actionPlan->id
                            ]);
                        }
                    } else {
                        Log::warning('Coordinator not found or has no user_id during update', [
                            'coordinator_id' => $coordinatorId
                        ]);
                    }
                }
            }

            return response()->json([
                'message' => 'Action plan updated successfully',
                'action_plan' => $actionPlan
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating action plan: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update action plan'], 500);
        }
    }

    /**
     * Cancel an action plan.
     */
    public function cancelActionPlan(Request $request, $id)
    {
        try {
            // Validate the request
            $request->validate([
                'cancellation_reason' => 'required|string|min:5|max:500',
            ]);

            $actionPlan = ActionPlan::where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Only allow cancellation if the action plan is still in 'planned' status
            if ($actionPlan->status !== 'planned') {
                return response()->json(['error' => 'Cannot cancel a completed action plan'], 403);
            }

            $actionPlan->update([
                'status' => 'cancelled',
                'cancellation_reason' => $request->cancellation_reason
            ]);

            Log::info('Action plan cancelled', [
                'action_plan_id' => $id,
                'scientist_id' => Auth::id(),
                'cancellation_reason' => $request->cancellation_reason
            ]);

            return response()->json([
                'message' => 'Action plan cancelled successfully'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed for action plan cancellation', [
                'errors' => $e->errors()
            ]);
            return response()->json(['error' => 'Please provide a valid reason for cancellation (minimum 5 characters)'], 422);
        } catch (\Exception $e) {
            Log::error('Error cancelling action plan: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to cancel action plan'], 500);
        }
    }

    /**
     * Submit post-action report.
     */
    public function submitReport(Request $request, $id)
    {
        // Log the request for debugging
        Log::info('Action plan report submission received', [
            'id' => $id,
            'request_data' => $request->except(['photos', 'photo1', 'photo2', 'photo3', 'photo4']),
            'has_photos' => $request->hasFile('photos'),
            'photo_count' => $request->hasFile('photos') ? count($request->file('photos')) : 0,
            'scientist_objectives_met' => $request->scientist_objectives_met,
            'scientist_objectives_met_type' => gettype($request->scientist_objectives_met),
            'objectives_met' => $request->objectives_met,
            'objectives_met_type' => gettype($request->objectives_met)
        ]);

        // try {
        //     $request->validate([
        //         'male_participants' => 'required|integer|min:0',
        //         'female_participants' => 'required|integer|min:0',
        //         'transgender_participants' => 'required|integer|min:0',
        //         'st_participants' => 'required|integer|min:0',
        //         'sc_participants' => 'required|integer|min:0',
        //         'general_participants' => 'required|integer|min:0',
        //         'obc_participants' => 'required|integer|min:0',
        //         'photo1' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        //         'photo2' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        //         'photo3' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        //         'photo4' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        //         'gis_location' => 'required|string|regex:/^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/',
        //         // Scientist Feedback with Metrics fields
        //         'scientist_event_success_rating' => 'required|integer|min:1|max:5',
        //         'scientist_challenges_faced' => 'required|string',
        //         'scientist_suggestions_for_improvement' => 'required|string',
        //         'scientist_objectives_met' => 'required',
        //         'scientist_self_assessed_learning_outcome' => 'required|string',
        //     ]);
        // } catch (\Illuminate\Validation\ValidationException $e) {
        //     Log::error('Validation failed for action plan report', [
        //         'errors' => $e->errors()
        //     ]);
        //     return response()->json(['error' => 'Validation failed: ' . implode(', ', array_map(function($arr) {
        //         return implode(', ', $arr);
        //     }, $e->errors()))], 422);
        // }

        try {
            $actionPlan = ActionPlan::where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Only allow reporting if the action plan is in 'planned' status
            if ($actionPlan->status !== 'planned') {
                return response()->json(['error' => 'Cannot submit report for a cancelled action plan'], 403);
            }

            // Check if the planned date has passed
            if ($actionPlan->planned_date > now()) {
                return response()->json(['error' => 'Cannot submit report before the planned date'], 403);
            }

            // Upload individual photos
            $photo1Path = null;
            $photo2Path = null;
            $photo3Path = null;
            $photo4Path = null;
            $mediaPhotoPath = null;

            // Process Photo 1
            if ($request->hasFile('photo1')) {
                try {
                    $photo = $request->file('photo1');
                    Log::info('Processing photo1', [
                        'original_name' => $photo->getClientOriginalName(),
                        'mime_type' => $photo->getMimeType(),
                        'size' => $photo->getSize()
                    ]);

                    $photo1Path = $photo->store('action-plan-photos', 'public');
                    Log::info('Photo1 uploaded successfully', ['path' => $photo1Path]);
                } catch (\Exception $e) {
                    Log::error('Error uploading photo1', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            // Process Photo 2
            if ($request->hasFile('photo2')) {
                try {
                    $photo = $request->file('photo2');
                    Log::info('Processing photo2', [
                        'original_name' => $photo->getClientOriginalName(),
                        'mime_type' => $photo->getMimeType(),
                        'size' => $photo->getSize()
                    ]);

                    $photo2Path = $photo->store('action-plan-photos', 'public');
                    Log::info('Photo2 uploaded successfully', ['path' => $photo2Path]);
                } catch (\Exception $e) {
                    Log::error('Error uploading photo2', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            // Process Photo 3
            if ($request->hasFile('photo3')) {
                try {
                    $photo = $request->file('photo3');
                    Log::info('Processing photo3', [
                        'original_name' => $photo->getClientOriginalName(),
                        'mime_type' => $photo->getMimeType(),
                        'size' => $photo->getSize()
                    ]);

                    $photo3Path = $photo->store('action-plan-photos', 'public');
                    Log::info('Photo3 uploaded successfully', ['path' => $photo3Path]);
                } catch (\Exception $e) {
                    Log::error('Error uploading photo3', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            // Process Photo 4
            if ($request->hasFile('photo4')) {
                try {
                    $photo = $request->file('photo4');
                    Log::info('Processing photo4', [
                        'original_name' => $photo->getClientOriginalName(),
                        'mime_type' => $photo->getMimeType(),
                        'size' => $photo->getSize()
                    ]);

                    $photo4Path = $photo->store('action-plan-photos', 'public');
                    Log::info('Photo4 uploaded successfully', ['path' => $photo4Path]);
                } catch (\Exception $e) {
                    Log::error('Error uploading photo4', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            // Process Media Photo
            if ($request->hasFile('media_photo')) {
                try {
                    $photo = $request->file('media_photo');
                    Log::info('Processing media_photo', [
                        'original_name' => $photo->getClientOriginalName(),
                        'mime_type' => $photo->getMimeType(),
                        'size' => $photo->getSize()
                    ]);

                    $mediaPhotoPath = $photo->store('action-plan-photos', 'public');
                    Log::info('Media photo uploaded successfully', ['path' => $mediaPhotoPath]);
                } catch (\Exception $e) {
                    Log::error('Error uploading media photo', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            // For backward compatibility
            $photosPaths = [];
            if ($photo1Path) $photosPaths[] = $photo1Path;
            if ($photo2Path) $photosPaths[] = $photo2Path;
            if ($photo3Path) $photosPaths[] = $photo3Path;
            if ($photo4Path) $photosPaths[] = $photo4Path;

            // Process single media link and social media link (simplified)
            $mediaLink = $request->media_link ? trim($request->media_link) : null;
            $socialMediaLink = $request->social_media_link ? trim($request->social_media_link) : null;

            // Debug logging
            Log::info('Processing single links', [
                'media_link' => $mediaLink,
                'social_media_link' => $socialMediaLink
            ]);

            // Log what we're about to save
            Log::info('About to save ActionPlanFeedback', [
                'mediaLink' => $mediaLink,
                'socialMediaLink' => $socialMediaLink
            ]);

            // Create feedback in the new actionplan_feedback table
            $feedback = \App\Models\ActionPlanFeedback::create([
                'user_id' => Auth::id(),
                'action_plan_id' => $actionPlan->id,
                'event_success_rating' => $request->scientist_event_success_rating,
                'challenges_faced' => $request->scientist_challenges_faced,
                'suggestions_for_improvement' => $request->scientist_suggestions_for_improvement,
                'objectives_met' => is_array($request->scientist_objectives_met) ?
                    json_encode($request->scientist_objectives_met) : $request->scientist_objectives_met,
                'self_assessed_learning_outcome' => $request->scientist_self_assessed_learning_outcome,
                'male_participants' => $request->male_participants,
                'female_participants' => $request->female_participants,
                'transgender_participants' => $request->transgender_participants,
                'st_participants' => $request->st_participants,
                'sc_participants' => $request->sc_participants,
                'general_participants' => $request->general_participants,
                'obc_participants' => $request->obc_participants,
                'photo1' => $photo1Path,
                'photo2' => $photo2Path,
                'photo3' => $photo3Path,
                'photo4' => $photo4Path,
                'gis_location' => $request->gis_location,
                // VIP Participation fields
                'vip_participated' => $request->has('vip_participated') ? true : false,
                'vip_mla_mp' => $request->has('vip_mla_mp') ? true : false,
                'vip_ms_jst_director' => $request->has('vip_ms_jst_director') ? true : false,
                'vip_local_pr' => $request->has('vip_local_pr') ? true : false,
                'vip_state_official' => $request->has('vip_state_official') ? true : false,
                'vip_others' => $request->has('vip_others') ? true : false,
                'vip_others_details' => $request->vip_others_details,
                // Media Coverage fields
                'media_coverage' => $request->has('media_coverage') ? true : false,
                'media_newspaper' => $request->has('media_newspaper') ? true : false,
                'media_tv_news' => $request->has('media_tv_news') ? true : false,
                'media_radio' => $request->has('media_radio') ? true : false,
                'media_link' => $mediaLink,
                'media_photo' => $mediaPhotoPath,
                // Social Media Post Link
                'social_media_link' => $socialMediaLink
            ]);

            // Log what was actually saved
            Log::info('ActionPlanFeedback saved', [
                'feedback_id' => $feedback->id,
                'saved_media_link' => $feedback->media_link,
                'saved_social_media_link' => $feedback->social_media_link
            ]);

            // Update action plan status
            $actionPlan->update([
                'status' => 'completed',
                'scientist_feedback_status' => 'submitted'
            ]);


            // Log success
            Log::info('Action plan report submitted successfully', [
                'action_plan_id' => $actionPlan->id,
                'photo_count' => count($photosPaths),
                'scientist_objectives_met' => $actionPlan->scientist_objectives_met
            ]);

            return response()->json([
                'message' => 'Action plan report submitted successfully',
                'action_plan' => $actionPlan,
                'photos' => $photosPaths
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting action plan report: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'action_plan_id' => $id,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'error' => 'Failed to submit action plan report: ' . $e->getMessage()
            ], 500);
        }
    }
}
