<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\ParticipantFeedback;
use App\Models\DistrictStateCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    /**
     * Display the notification settings page.
     */
    public function index()
    {
        // Get the current user's district state coordinator record
        $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

        // If no coordinator record exists, show error
        if (!$coordinator) {
            return redirect()->route('district-state-coordinator.dashboard')
                ->with('error', 'District state coordinator record not found. Please contact the administrator.');
        }

        // Get notification settings
        $settings = $coordinator->notification_settings ?? DistrictStateCoordinator::getDefaultNotificationSettings();

        return view('district-state-coordinator.notifications.index', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update notification settings.
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'new_feedback' => 'boolean',
            'daily_summary' => 'boolean',
            'weekly_summary' => 'boolean',
            'low_rating_alerts' => 'boolean',
            'low_rating_threshold' => 'required|integer|min:1|max:10',
        ]);

        // Get the current user's district state coordinator record
        $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

        if (!$coordinator) {
            return redirect()->route('district-state-coordinator.dashboard')
                ->with('error', 'District state coordinator record not found. Please contact the administrator.');
        }

        // Update settings
        $coordinator->notification_settings = [
            'new_feedback' => (bool) $request->input('new_feedback', false),
            'daily_summary' => (bool) $request->input('daily_summary', false),
            'weekly_summary' => (bool) $request->input('weekly_summary', false),
            'low_rating_alerts' => (bool) $request->input('low_rating_alerts', false),
            'low_rating_threshold' => (int) $request->input('low_rating_threshold', 3),
        ];

        $coordinator->save();

        return redirect()->route('district-state-coordinator.notifications.index')
            ->with('success', 'Notification settings updated successfully');
    }

    /**
     * Send a test notification.
     */
    public function sendTestNotification(Request $request)
    {
        try {
            $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // For now, just return a success message
            // In a real implementation, you would integrate with an email service
            Log::info('Test notification requested by district state coordinator: ' . $coordinator->id);

            return response()->json([
                'message' => 'Test notification sent successfully',
                'timestamp' => now()->toDateTimeString(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error sending test notification: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to send test notification'], 500);
        }
    }

    /**
     * Get notification history.
     */
    public function getNotificationHistory()
    {
        try {
            $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get recent feedback that would trigger notifications
            $recentFeedback = ParticipantFeedback::whereHas('actionPlan', function ($query) use ($districtIds) {
                $query->whereIn('district_id', $districtIds);
            })
                ->where('created_at', '>=', now()->subDays(30))
                ->with(['actionPlan:id,title,planned_date,district_id'])
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();

            // Format notification history
            $notifications = $recentFeedback->map(function ($feedback) {
                return [
                    'id' => $feedback->id,
                    'type' => 'new_feedback',
                    'title' => 'New Feedback Received',
                    'message' => "New feedback received for event: {$feedback->actionPlan->title}",
                    'event_title' => $feedback->actionPlan->title,
                    'rating' => $feedback->benefit_rating,
                    'created_at' => $feedback->created_at->toDateTimeString(),
                    'formatted_date' => $feedback->created_at->diffForHumans(),
                ];
            });

            return response()->json($notifications);

        } catch (\Exception $e) {
            Log::error('Error getting notification history: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get notification history'], 500);
        }
    }

    /**
     * Get notification summary.
     */
    public function getNotificationSummary()
    {
        try {
            $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get notification settings
            $settings = $coordinator->notification_settings ?? DistrictStateCoordinator::getDefaultNotificationSettings();

            // Calculate summary statistics
            $today = now()->startOfDay();
            $thisWeek = now()->startOfWeek();

            $summary = [
                'settings' => $settings,
                'stats' => [
                    'new_feedback_today' => ParticipantFeedback::whereHas('actionPlan', function ($query) use ($districtIds) {
                        $query->whereIn('district_id', $districtIds);
                    })
                        ->where('created_at', '>=', $today)
                        ->count(),
                    'new_feedback_this_week' => ParticipantFeedback::whereHas('actionPlan', function ($query) use ($districtIds) {
                        $query->whereIn('district_id', $districtIds);
                    })
                        ->where('created_at', '>=', $thisWeek)
                        ->count(),
                    'low_rating_alerts' => $settings['low_rating_alerts'] ? 
                        ParticipantFeedback::whereHas('actionPlan', function ($query) use ($districtIds) {
                            $query->whereIn('district_id', $districtIds);
                        })
                        ->where('benefit_rating', '<=', $settings['low_rating_threshold'])
                        ->where('created_at', '>=', $thisWeek)
                        ->count() : 0,
                ],
            ];

            return response()->json($summary);

        } catch (\Exception $e) {
            Log::error('Error getting notification summary: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get notification summary'], 500);
        }
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request)
    {
        $request->validate([
            'notification_id' => 'required|integer',
        ]);

        try {
            // In a real implementation, you would update a notifications table
            // For now, just log the action
            Log::info('Notification marked as read: ' . $request->notification_id . ' by user: ' . Auth::id());

            return response()->json(['message' => 'Notification marked as read']);

        } catch (\Exception $e) {
            Log::error('Error marking notification as read: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to mark notification as read'], 500);
        }
    }
}
