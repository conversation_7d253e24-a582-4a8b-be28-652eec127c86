<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" style="color: black;">Invite District State Coordinators</h5>
                    <a href="<?php echo e(route('scientist.action-plans')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Action Plans
                    </a>
                </div>
                <div class="card-body">
                    <!-- Action Plan Details -->
                    <div class="alert alert-info">
                        <h6 style="color: black;"><strong>Action Plan Details:</strong></h6>
                        <p style="color: black;"><strong>Title:</strong> <?php echo e($actionPlan->title); ?></p>
                        <p style="color: black;"><strong>Type:</strong> <?php echo e(ucfirst(str_replace('_', ' ', $actionPlan->type))); ?></p>
                        <p style="color: black;"><strong>Location:</strong> <?php echo e($actionPlan->location); ?></p>
                        <p style="color: black;"><strong>Planned Date:</strong> <?php echo e($actionPlan->planned_date->format('d M Y')); ?></p>
                        <p style="color: black;"><strong>Expected Participants:</strong> <?php echo e($actionPlan->expected_participants); ?></p>
                    </div>

                    <!-- DSC Selection Form -->
                    <div class="row">
                        <div class="col-md-8">
                            <h6 style="color: black;">Available District State Coordinators</h6>
                            <p style="color: #666; font-size: 0.9em; margin-bottom: 15px;">
                                <i class="fas fa-info-circle"></i>
                                Showing DSCs assigned to your district and state only
                            </p>
                            <div id="dsc-list" class="mb-3">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p style="color: black;">Loading DSCs...</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 style="color: black;">Invitation Message</h6>
                            <form id="invitation-form">
                                <input type="hidden" id="action_plan_id" value="<?php echo e($actionPlan->id); ?>">
                                <div class="mb-3">
                                    <label for="invitation_message" class="form-label" style="color: black;">Message to DSCs</label>
                                    <textarea class="form-control" id="invitation_message" name="invitation_message" rows="6" required 
                                              placeholder="Write your invitation message here...">Dear District State Coordinator,

I would like to invite you to participate in my action plan: <?php echo e($actionPlan->title); ?>


This <?php echo e(str_replace('_', ' ', $actionPlan->type)); ?> is scheduled for <?php echo e($actionPlan->planned_date->format('d M Y')); ?> at <?php echo e($actionPlan->location); ?>.

We expect <?php echo e($actionPlan->expected_participants); ?> participants and would greatly appreciate your expertise and support.

Thank you for your consideration.

Best regards,
<?php echo e(Auth::user()->name); ?></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary w-100" id="send-invitations-btn" disabled>
                                    <i class="fas fa-paper-plane"></i> Send Invitations
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Already Invited DSCs -->
                    <div class="mt-4">
                        <h6 style="color: black;">Already Invited DSCs</h6>
                        <div id="invited-dscs" class="mb-3">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <small style="color: black;">Loading invited DSCs...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedDSCs = [];
    
    // Load available DSCs
    loadAvailableDSCs();
    
    // Load already invited DSCs
    loadInvitedDSCs();
    
    // Handle form submission
    document.getElementById('invitation-form').addEventListener('submit', function(e) {
        e.preventDefault();
        sendInvitations();
    });
    
    function loadAvailableDSCs() {
        fetch('<?php echo e(route("scientist.dsc-invitation.available")); ?>')
            .then(response => response.json())
            .then(data => {
                displayDSCs(data);
            })
            .catch(error => {
                console.error('Error loading DSCs:', error);
                document.getElementById('dsc-list').innerHTML = 
                    '<div class="alert alert-danger">Error loading DSCs. Please try again.</div>';
            });
    }
    
    function displayDSCs(dscs) {
        const container = document.getElementById('dsc-list');
        
        if (dscs.length === 0) {
            container.innerHTML = '<div class="alert alert-warning">No District State Coordinators found for your district or state. Please contact your administrator to assign DSCs to your area.</div>';
            return;
        }
        
        let html = '';
        dscs.forEach(dsc => {
            const expertiseAreas = Array.isArray(dsc.expertise_areas) ? dsc.expertise_areas.join(', ') : (dsc.expertise_areas || 'Not specified');
            
            html += `
                <div class="card mb-2">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input dsc-checkbox" type="checkbox" value="${dsc.id}" id="dsc-${dsc.id}">
                            <label class="form-check-label" for="dsc-${dsc.id}" style="color: black;">
                                <strong>${dsc.name}</strong>
                                <br><small class="text-muted">${dsc.designation || 'No designation'}</small>
                                <br><small class="text-muted">Email: ${dsc.email || 'Not provided'}</small>
                                <br><small class="text-muted">Phone: ${dsc.phone_number || 'Not provided'}</small>
                                <br><small class="text-muted">Expertise: ${expertiseAreas}</small>
                            </label>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.dsc-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedDSCs);
        });
    }
    
    function updateSelectedDSCs() {
        selectedDSCs = Array.from(document.querySelectorAll('.dsc-checkbox:checked')).map(cb => cb.value);
        document.getElementById('send-invitations-btn').disabled = selectedDSCs.length === 0;
    }
    
    function sendInvitations() {
        const formData = {
            action_plan_id: document.getElementById('action_plan_id').value,
            selected_dscs: selectedDSCs,
            invitation_message: document.getElementById('invitation_message').value,
            _token: '<?php echo e(csrf_token()); ?>'
        };
        
        const submitBtn = document.getElementById('send-invitations-btn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        
        fetch('<?php echo e(route("scientist.dsc-invitation.send")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
            } else {
                alert(data.message);
                // Reload the page to show updated status
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error sending invitations:', error);
            alert('Error sending invitations. Please try again.');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Invitations';
        });
    }
    
    function loadInvitedDSCs() {
        fetch(`<?php echo e(route("scientist.dsc-invitation.invited", $actionPlan->id)); ?>`)
            .then(response => response.json())
            .then(data => {
                displayInvitedDSCs(data);
            })
            .catch(error => {
                console.error('Error loading invited DSCs:', error);
                document.getElementById('invited-dscs').innerHTML = 
                    '<div class="alert alert-danger">Error loading invited DSCs.</div>';
            });
    }
    
    function displayInvitedDSCs(invitations) {
        const container = document.getElementById('invited-dscs');
        
        if (invitations.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No DSCs have been invited yet.</div>';
            return;
        }
        
        let html = '<div class="row">';
        invitations.forEach(invitation => {
            const statusClass = invitation.status === 'accepted' ? 'success' : 
                               invitation.status === 'rejected' ? 'danger' : 'warning';
            
            html += `
                <div class="col-md-6 mb-2">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title" style="color: black;">${invitation.coordinator.name}</h6>
                            <p class="card-text" style="color: black;">
                                <small>Email: ${invitation.coordinator.email}</small><br>
                                <span class="badge bg-${statusClass}">${invitation.status.toUpperCase()}</span>
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        container.innerHTML = html;
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/scientist/dsc-invitation/index.blade.php ENDPATH**/ ?>