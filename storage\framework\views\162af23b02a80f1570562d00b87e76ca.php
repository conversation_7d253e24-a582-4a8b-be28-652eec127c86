<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Scientist Management')); ?></h5>
                    <div>
                        
                        <a href="<?php echo e(route('super-admin.dashboard')); ?>" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View and manage all scientists in the system.</li>
                            <li>Click on a scientist to view their details and district assignments.</li>
                            
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Designation</th>
                                    <th>Districts</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="scientistsTableBody">
                                <!-- Scientists will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Scientist Form Modal -->
                    <div class="modal fade" id="scientistModal" tabindex="-1" aria-labelledby="scientistModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="scientistModalLabel">Add New Scientist</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="scientistForm">
                                        <input type="hidden" id="scientistId" name="id">
                                        <input type="hidden" id="isNew" name="is_new" value="true">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="name">Name:</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="email">Email:</label>
                                                    <input type="email" class="form-control" id="email" name="email" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="phone_number">Phone Number:</label>
                                                    <input type="text" class="form-control" id="phone_number" name="phone_number" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="designation">Designation:</label>
                                                    <input type="text" class="form-control" id="designation" name="designation" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="password">Password:</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="password" name="password">
                                                        <button class="btn btn-outline-secondary" type="button" id="toggleScientistPassword">
                                                            <i class="fas fa-eye" id="scientistPasswordIcon"></i>
                                                        </button>
                                                    </div>
                                                    <small id="passwordHelpText" class="form-text text-muted">Leave blank to keep current password.</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="districtAssignments" class="mt-4">
                                            <h5>District Assignments</h5>
                                            <div class="alert alert-info">
                                                District assignments are managed through the District Management page.
                                            </div>
                                            <div id="assignedDistrictsList">
                                                <!-- Assigned districts will be shown here -->
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="saveScientistBtn">Save Scientist</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this scientist? This action cannot be undone.</p>
                                    <p>This will also remove the scientist from any district assignments.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Scientist</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const scientistsTableBody = document.getElementById('scientistsTableBody');
        const addScientistBtn = document.getElementById('addScientistBtn');
        const scientistForm = document.getElementById('scientistForm');
        const saveScientistBtn = document.getElementById('saveScientistBtn');
        const scientistModal = new bootstrap.Modal(document.getElementById('scientistModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const passwordHelpText = document.getElementById('passwordHelpText');
        let currentScientistId = null;

        // Password toggle functionality
        const toggleScientistPassword = document.getElementById('toggleScientistPassword');
        if (toggleScientistPassword) {
            toggleScientistPassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const passwordIcon = document.getElementById('scientistPasswordIcon');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    passwordIcon.classList.remove('fa-eye');
                    passwordIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    passwordIcon.classList.remove('fa-eye-slash');
                    passwordIcon.classList.add('fa-eye');
                }
            });
        }

        // Load scientists
        loadScientists();

        // Add scientist button click
        if (addScientistBtn) {
            addScientistBtn.addEventListener('click', function() {
                resetForm();
                document.getElementById('scientistModalLabel').textContent = 'Add New Scientist';
                document.getElementById('isNew').value = 'true';
                document.getElementById('districtAssignments').style.display = 'none';
                passwordHelpText.style.display = 'none';
                scientistModal.show();
            });
        }

        // Save scientist button click
        saveScientistBtn.addEventListener('click', function() {
            if (!scientistForm.checkValidity()) {
                scientistForm.reportValidity();
                return;
            }

            const formData = new FormData(scientistForm);
            const data = {};
            formData.forEach((value, key) => data[key] = value);

            fetch('<?php echo e(route("super-admin.scientist-management.save")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    scientistModal.hide();
                    loadScientists();
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving scientist:', error);
                alert('An error occurred while saving the scientist information.');
            });
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentScientistId) {
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                const url = `<?php echo e(route("super-admin.scientist-management.delete", ["id" => "ID"])); ?>`.replace('ID', currentScientistId);

                console.log('Attempting to delete scientist:', {
                    id: currentScientistId,
                    url: url,
                    token: token
                });

                // Add alert to confirm the action is triggered
                console.log('Delete button clicked, making request...');

                fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                })
                .then(async response => {
                    const responseData = await response.text();
                    console.log('Delete response:', {
                        status: response.status,
                        statusText: response.statusText,
                        data: responseData
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}, message: ${responseData}`);
                    }
                    return JSON.parse(responseData);
                })
                .then(data => {
                    console.log('Delete success:', data);
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadScientists();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting scientist:', error);
                    alert(`Error: ${error.message}`);
                });
            } else {
                console.error('No scientist ID selected for deletion');
                alert('No scientist selected for deletion');
            }
        });

        // Load scientists function
        function loadScientists() {
            fetch('<?php echo e(route("super-admin.scientist-management.scientists")); ?>')
                .then(response => response.json())
                .then(data => {
                    scientistsTableBody.innerHTML = '';

                    data.forEach(scientist => {
                        const row = document.createElement('tr');

                        // Create district badges
                        let districtBadges = '';
                        if (scientist.districts && scientist.districts.length > 0) {
                            scientist.districts.forEach(district => {
                                const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                                districtBadges += `<span class="badge ${badgeClass} me-1">${district.district} (${district.status})</span>`;
                            });
                        } else {
                            districtBadges = '<span class="text-muted">No districts assigned</span>';
                        }

                        row.innerHTML = `
                            <td>${scientist.name}</td>
                            <td>${scientist.email}</td>
                            <td>${scientist.phone_number || ''}</td>
                            <td>${scientist.designation || ''}</td>
                            <td>${districtBadges}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="${scientist.id}">Edit</button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${scientist.id}">Delete</button>
                            </td>
                        `;

                        scientistsTableBody.appendChild(row);
                    });

                    // Add event listeners to edit buttons
                    document.querySelectorAll('.edit-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const scientistId = this.dataset.id;
                            loadScientistDetails(scientistId);
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            currentScientistId = this.dataset.id;
                            deleteConfirmModal.show();
                        });
                    });
                })
                .catch(error => console.error('Error loading scientists:', error));
        }

        // Load scientist details function
        function loadScientistDetails(id) {
            fetch(`<?php echo e(url("super-admin/scientist-management/scientist")); ?>/${id}`)
                .then(response => response.json())
                .then(scientist => {
                    resetForm();

                    document.getElementById('scientistId').value = scientist.id;
                    document.getElementById('name').value = scientist.name;
                    document.getElementById('email').value = scientist.email;
                    document.getElementById('phone_number').value = scientist.phone_number || '';
                    document.getElementById('designation').value = scientist.designation || '';
                    document.getElementById('isNew').value = 'false';
                    document.getElementById('scientistModalLabel').textContent = 'Edit Scientist';
                    passwordHelpText.style.display = 'block';

                    // Show district assignments
                    document.getElementById('districtAssignments').style.display = 'block';
                    const assignedDistrictsList = document.getElementById('assignedDistrictsList');
                    assignedDistrictsList.innerHTML = '';

                    if (scientist.districts && scientist.districts.length > 0) {
                        const ul = document.createElement('ul');
                        ul.className = 'list-group';

                        scientist.districts.forEach(district => {
                            const li = document.createElement('li');
                            li.className = 'list-group-item';
                            const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                            li.innerHTML = `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${district.district}</strong> (${district.state})
                                        <span class="badge ${badgeClass} ms-2">${district.status}</span>
                                    </div>
                                </div>
                            `;
                            ul.appendChild(li);
                        });

                        assignedDistrictsList.appendChild(ul);
                    } else {
                        assignedDistrictsList.innerHTML = '<div class="alert alert-warning">No districts assigned to this scientist.</div>';
                    }

                    scientistModal.show();
                })
                .catch(error => console.error('Error loading scientist details:', error));
        }

        // Reset form function
        function resetForm() {
            scientistForm.reset();
            document.getElementById('scientistId').value = '';
            document.getElementById('assignedDistrictsList').innerHTML = '';
        }
    });
</script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\_csb-mrma.in (2)\csb\resources\views/super-admin/scientist-management/index.blade.php ENDPATH**/ ?>