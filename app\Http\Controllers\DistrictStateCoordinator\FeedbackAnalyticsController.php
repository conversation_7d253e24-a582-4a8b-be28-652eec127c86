<?php

namespace App\Http\Controllers\DistrictStateCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\ParticipantFeedback;
use App\Models\DistrictStateCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FeedbackAnalyticsController extends Controller
{
    /**
     * Display the feedback analytics dashboard.
     */
    public function index()
    {
        return view('district-state-coordinator.feedback-analytics.index');
    }

    /**
     * Get analytics data for all events in the coordinator's districts.
     */
    public function getAnalyticsData(Request $request)
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Get date range filter if provided
            $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subMonths(3);
            $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();

            // Get action plans in these districts
            $actionPlans = ActionPlan::whereIn('district_id', $districtIds)
                ->where('status', 'completed')
                ->whereBetween('planned_date', [$startDate, $endDate])
                ->with(['district:id,state,district'])
                ->get();

            $actionPlanIds = $actionPlans->pluck('id')->toArray();

            // Get feedback for these action plans
            $feedback = ParticipantFeedback::whereIn('action_plan_id', $actionPlanIds)
                ->with('actionPlan:id,title,planned_date,district_id')
                ->get();

            // Calculate overall statistics
            $stats = [
                'total_events' => count($actionPlanIds),
                'total_feedback' => $feedback->count(),
                'avg_feedback_per_event' => $feedback->count() > 0 ? round($feedback->count() / count($actionPlanIds), 1) : 0,
                'avg_benefit_rating' => $feedback->avg('benefit_rating') ? round($feedback->avg('benefit_rating'), 1) : 0,
                'avg_speaker_rating' => $feedback->avg('speaker_rating') ? round($feedback->avg('speaker_rating'), 1) : 0,
                'would_recommend_percentage' => $feedback->count() > 0
                    ? round(($feedback->where('would_recommend', true)->count() / $feedback->count()) * 100)
                    : 0,
                'by_source' => [
                    'web' => $feedback->where('source', 'web')->count(),
                    'excel' => $feedback->where('source', 'excel')->count(),
                    'mobile' => $feedback->where('source', 'mobile')->count(),
                ],
            ];

            // Calculate trend data (monthly)
            $trendData = $this->calculateTrendData($feedback, $startDate, $endDate);

            // Calculate district-wise statistics
            $districtStats = $this->calculateDistrictStats($feedback, $actionPlans);

            // Calculate topic analysis
            $topicAnalysis = $this->analyzeTopics($feedback);

            // Calculate rating distribution
            $ratingDistribution = [
                'benefit' => $this->calculateRatingDistribution($feedback, 'benefit_rating', 10),
                'speaker' => $this->calculateRatingDistribution($feedback, 'speaker_rating', 5),
            ];

            return response()->json([
                'stats' => $stats,
                'trend_data' => $trendData,
                'district_stats' => $districtStats,
                'topic_analysis' => $topicAnalysis,
                'rating_distribution' => $ratingDistribution,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting analytics data: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get analytics data'], 500);
        }
    }

    /**
     * Calculate trend data for feedback over time.
     */
    private function calculateTrendData($feedback, $startDate, $endDate)
    {
        $trendData = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current <= $endDate) {
            $monthFeedback = $feedback->filter(function ($item) use ($current) {
                return Carbon::parse($item->actionPlan->planned_date)->format('Y-m') === $current->format('Y-m');
            });

            $trendData[] = [
                'month' => $current->format('Y-m'),
                'month_name' => $current->format('M Y'),
                'feedback_count' => $monthFeedback->count(),
                'avg_benefit_rating' => $monthFeedback->avg('benefit_rating') ? round($monthFeedback->avg('benefit_rating'), 1) : 0,
                'avg_speaker_rating' => $monthFeedback->avg('speaker_rating') ? round($monthFeedback->avg('speaker_rating'), 1) : 0,
            ];

            $current->addMonth();
        }

        return $trendData;
    }

    /**
     * Calculate district-wise statistics.
     */
    private function calculateDistrictStats($feedback, $actionPlans)
    {
        $districtStats = [];

        foreach ($actionPlans->groupBy('district_id') as $districtId => $plans) {
            $district = $plans->first()->district;
            $planIds = $plans->pluck('id')->toArray();
            $districtFeedback = $feedback->whereIn('action_plan_id', $planIds);

            $districtStats[] = [
                'district_id' => $districtId,
                'district_name' => $district->state . ' - ' . $district->district,
                'events_count' => count($planIds),
                'feedback_count' => $districtFeedback->count(),
                'avg_benefit_rating' => $districtFeedback->avg('benefit_rating') ? round($districtFeedback->avg('benefit_rating'), 1) : 0,
                'avg_speaker_rating' => $districtFeedback->avg('speaker_rating') ? round($districtFeedback->avg('speaker_rating'), 1) : 0,
                'would_recommend_percentage' => $districtFeedback->count() > 0
                    ? round(($districtFeedback->where('would_recommend', true)->count() / $districtFeedback->count()) * 100)
                    : 0,
            ];
        }

        return collect($districtStats)->sortByDesc('feedback_count')->values()->all();
    }

    /**
     * Analyze topics mentioned in feedback.
     */
    private function analyzeTopics($feedback)
    {
        $topics = [];
        $suggestions = $feedback->pluck('suggestions')->filter()->flatten();

        // Simple keyword analysis
        $keywords = ['training', 'demonstration', 'field', 'technology', 'support', 'guidance', 'equipment', 'seed', 'fertilizer'];

        foreach ($keywords as $keyword) {
            $count = $suggestions->filter(function ($suggestion) use ($keyword) {
                return stripos($suggestion, $keyword) !== false;
            })->count();

            if ($count > 0) {
                $topics[] = [
                    'topic' => ucfirst($keyword),
                    'mentions' => $count,
                ];
            }
        }

        return collect($topics)->sortByDesc('mentions')->take(10)->values()->all();
    }

    /**
     * Calculate rating distribution.
     */
    private function calculateRatingDistribution($feedback, $ratingField, $maxRating)
    {
        $distribution = [];

        for ($i = 1; $i <= $maxRating; $i++) {
            $count = $feedback->where($ratingField, $i)->count();
            $distribution[] = [
                'rating' => $i,
                'count' => $count,
                'percentage' => $feedback->count() > 0 ? round(($count / $feedback->count()) * 100, 1) : 0,
            ];
        }

        return $distribution;
    }

    /**
     * Get detailed feedback for a specific event.
     */
    public function getEventFeedback($eventId)
    {
        try {
            // Get the current user's district state coordinator record
            $coordinator = DistrictStateCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'District state coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = $coordinator->districts()->pluck('state_data.id');

            // Verify the event belongs to one of the coordinator's districts
            $actionPlan = ActionPlan::whereIn('district_id', $districtIds)
                ->where('id', $eventId)
                ->with(['district:id,state,district'])
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Event not found in your districts'], 404);
            }

            // Get feedback for this event
            $feedback = ParticipantFeedback::where('action_plan_id', $eventId)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'event' => $actionPlan,
                'feedback' => $feedback,
                'stats' => [
                    'total_feedback' => $feedback->count(),
                    'avg_benefit_rating' => $feedback->avg('benefit_rating') ? round($feedback->avg('benefit_rating'), 1) : 0,
                    'avg_speaker_rating' => $feedback->avg('speaker_rating') ? round($feedback->avg('speaker_rating'), 1) : 0,
                    'would_recommend_percentage' => $feedback->count() > 0
                        ? round(($feedback->where('would_recommend', true)->count() / $feedback->count()) * 100)
                        : 0,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting event feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get event feedback'], 500);
        }
    }
}
