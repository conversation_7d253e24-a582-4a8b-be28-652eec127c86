<?php

use App\Http\Controllers\DistrictStateCoordinator\DashboardController;
use App\Http\Controllers\DistrictStateCoordinator\ProfileController;
use App\Http\Controllers\DistrictStateCoordinator\RequestsController;
use App\Http\Controllers\DistrictStateCoordinator\DistrictController;
// use App\Http\Controllers\DistrictStateCoordinator\ScientistController; // Hidden - matching zonal coordinator
use App\Http\Controllers\DistrictStateCoordinator\EventController;
// use App\Http\Controllers\DistrictStateCoordinator\PerformanceController; // Hidden - matching zonal coordinator
use App\Http\Controllers\DistrictStateCoordinator\ScientistFeedbackController;
// use App\Http\Controllers\DistrictStateCoordinator\FeedbackAnalyticsController; // Hidden - matching zonal coordinator
// use App\Http\Controllers\DistrictStateCoordinator\NotificationController; // Hidden - matching zonal coordinator
use Illuminate\Support\Facades\Route;

// District State Coordinator Routes
Route::middleware(['auth', 'role:district_state_coordinator'])->prefix('district-state-coordinator')->name('district-state-coordinator.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Districts Management
    Route::prefix('districts')->name('districts.')->group(function () {
        Route::get('/', [DistrictController::class, 'index'])->name('index');
        Route::get('/get-districts', [DistrictController::class, 'getDistricts'])->name('get-districts');
        Route::get('/district/{id}', [DistrictController::class, 'getDistrictDetails'])->name('district');
    });

    // Scientists Management (Hidden - matching zonal coordinator)
    /*
    Route::prefix('scientists')->name('scientists.')->group(function () {
        Route::get('/', [ScientistController::class, 'index'])->name('index');
        Route::get('/get-scientists', [ScientistController::class, 'getScientists'])->name('get-scientists');
        Route::get('/scientist/{id}', [ScientistController::class, 'getScientistDetails'])->name('scientist');
        Route::get('/scientist-by-email/{email}', [ScientistController::class, 'getScientistByEmail'])->name('scientist-by-email');
        Route::get('/show/{id}', [ScientistController::class, 'show'])->name('show');
        Route::get('/assign', [ScientistController::class, 'showAssignForm'])->name('assign');
        Route::post('/assign-scientist', [ScientistController::class, 'assignScientist'])->name('assign-scientist');
    });
    */

    // Events Management
    Route::prefix('events')->name('events.')->group(function () {
        Route::get('/', [EventController::class, 'index'])->name('index');
        Route::get('/get-events', [EventController::class, 'getEvents'])->name('get-events');
        Route::get('/event/{id}', [EventController::class, 'getEventDetails'])->name('event');
        Route::get('/by-scientist/{id}', [EventController::class, 'getEventsByScientist'])->name('by-scientist');
    });

    // Performance Management (Hidden - matching zonal coordinator)
    /*
    Route::prefix('performance')->name('performance.')->group(function () {
        Route::get('/', [PerformanceController::class, 'index'])->name('index');
        Route::get('/get-performance-data', [PerformanceController::class, 'getPerformanceData'])->name('get-performance-data');
    });
    */

    // Scientist Feedback Management
    Route::prefix('scientist-feedback')->name('scientist-feedback.')->group(function () {
        Route::get('/', [ScientistFeedbackController::class, 'index'])->name('index');
        Route::get('/get-feedback', [ScientistFeedbackController::class, 'getFeedback'])->name('get-feedback');
        Route::get('/get-events', [ScientistFeedbackController::class, 'getEvents'])->name('get-events');
        Route::post('/submit-feedback', [ScientistFeedbackController::class, 'submitFeedback'])->name('submit-feedback');
        Route::get('/feedback-details/{id}', [ScientistFeedbackController::class, 'getFeedbackDetails'])->name('feedback-details');
    });

    // Feedback Analytics (Hidden - matching zonal coordinator)
    /*
    Route::prefix('feedback-analytics')->name('feedback-analytics.')->group(function () {
        Route::get('/', [FeedbackAnalyticsController::class, 'index'])->name('index');
        Route::get('/get-analytics-data', [FeedbackAnalyticsController::class, 'getAnalyticsData'])->name('get-analytics-data');
        Route::get('/event-feedback/{id}', [FeedbackAnalyticsController::class, 'getEventFeedback'])->name('event-feedback');
    });
    */

    // Notifications Management (Hidden - matching zonal coordinator)
    /*
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::post('/update-settings', [NotificationController::class, 'updateSettings'])->name('update-settings');
        Route::post('/send-test', [NotificationController::class, 'sendTestNotification'])->name('send-test');
        Route::get('/history', [NotificationController::class, 'getNotificationHistory'])->name('history');
        Route::get('/summary', [NotificationController::class, 'getNotificationSummary'])->name('summary');
        Route::post('/mark-read', [NotificationController::class, 'markAsRead'])->name('mark-read');
    });
    */

    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::post('/update-expertise', [ProfileController::class, 'updateExpertise'])->name('update-expertise');
    });

    // Request Management
    Route::prefix('requests')->name('requests.')->group(function () {
        Route::get('/', [RequestsController::class, 'index'])->name('index');
        Route::get('/accepted', [RequestsController::class, 'accepted'])->name('accepted');
        Route::get('/pending-requests', [RequestsController::class, 'getPendingRequests'])->name('pending-requests');
        Route::get('/accepted-requests', [RequestsController::class, 'getAcceptedRequests'])->name('accepted-requests');
        Route::post('/accept/{id}', [RequestsController::class, 'acceptRequest'])->name('accept');
        Route::post('/reject/{id}', [RequestsController::class, 'rejectRequest'])->name('reject');
    });
});
