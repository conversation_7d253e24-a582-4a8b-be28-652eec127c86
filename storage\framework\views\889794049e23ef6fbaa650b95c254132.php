<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-comments"></i> Public Feedback Details
                            </h4>
                            <small class="text-muted"><?php echo e($actionPlan->title); ?></small>
                        </div>
                        <div>
                            <a href="<?php echo e(route('scientist.feedback-import.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Action Plans
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong style="color: #000 !important;">Date:</strong><br>
                            <span style="color: #000 !important;"><?php echo e(\Carbon\Carbon::parse($actionPlan->planned_date)->format('d M Y, H:i')); ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong style="color: #000 !important;">Location:</strong><br>
                            <span style="color: #000 !important;"><?php echo e($actionPlan->location); ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong style="color: #000 !important;">Type:</strong><br>
                            <span class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' ', $actionPlan->type))); ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong style="color: #000 !important;">Status:</strong><br>
                            <?php if($actionPlan->status == 'completed'): ?>
                                <span class="badge bg-success">Completed</span>
                            <?php elseif($actionPlan->status == 'planned'): ?>
                                <span class="badge bg-warning text-dark">Planned</span>
                            <?php else: ?>
                                <span class="badge bg-danger"><?php echo e(ucfirst($actionPlan->status)); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo e($stats['total_responses']); ?></h3>
                            <p class="mb-0" style="color: #000 !important;">Total Responses</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo e($stats['avg_benefit_rating']); ?>/10</h3>
                            <p class="mb-0" style="color: #000 !important;">Avg Benefit Rating</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo e($stats['avg_speaker_rating']); ?>/5</h3>
                            <p class="mb-0" style="color: #000 !important;">Avg Speaker Rating</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo e($stats['recommend_percentage']); ?>%</h3>
                            <p class="mb-0" style="color: #000 !important;">Would Recommend</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feedback List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> All Feedback Responses (<?php echo e($feedback->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($feedback->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="color: #000 !important;">#</th>
                                        <th style="color: #000 !important;">Participant</th>
                                        <th style="color: #000 !important;">Benefit Rating</th>
                                        <th style="color: #000 !important;">Would Recommend</th>
                                        <th style="color: #000 !important;">Speaker Rating</th>
                                        <th style="color: #000 !important;">Most Helpful Topic</th>
                                        <th style="color: #000 !important;">Submitted</th>
                                        <th style="color: #000 !important;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $feedback; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $response): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td style="color: #000 !important;"><?php echo e($index + 1); ?></td>
                                            <td>
                                                <strong style="color: #000 !important;"><?php echo e($response->name ?: 'Anonymous'); ?></strong>
                                                <?php if($response->phone_number): ?>
                                                    <br><small style="color: #666 !important;"><?php echo e($response->phone_number); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($response->benefit_rating >= 8 ? 'success' : ($response->benefit_rating >= 6 ? 'warning text-dark' : 'danger')); ?>">
                                                    <?php echo e($response->benefit_rating); ?>/10
                                                </span>
                                            </td>
                                            <td>
                                                <?php if($response->would_recommend): ?>
                                                    <span class="badge bg-success">Yes</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($response->speaker_rating >= 4 ? 'success' : ($response->speaker_rating >= 3 ? 'warning text-dark' : 'danger')); ?>">
                                                    <?php echo e($response->speaker_rating); ?>/5
                                                </span>
                                            </td>
                                            <td>
                                                <?php if($response->most_helpful_topic): ?>
                                                    <span style="color: #000 !important; max-width: 200px; display: inline-block;" class="text-truncate" title="<?php echo e($response->most_helpful_topic); ?>">
                                                        <?php echo e(Str::limit($response->most_helpful_topic, 50)); ?>

                                                    </span>
                                                <?php else: ?>
                                                    <span style="color: #666 !important;">Not provided</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small style="color: #000 !important;"><?php echo e($response->created_at->format('d M Y, H:i')); ?></small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="viewDetails(<?php echo e($response->id); ?>)" title="View full feedback details">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No feedback received yet</h5>
                            <p class="text-muted">Participants haven't submitted any feedback for this action plan yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Details Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Feedback Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="feedbackModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewDetails(feedbackId) {
    // Find the feedback data
    const feedback = <?php echo json_encode($feedback, 15, 512) ?>;
    const selectedFeedback = feedback.find(f => f.id === feedbackId);

    if (selectedFeedback) {
        const modalBody = document.getElementById('feedbackModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 style="color: #000 !important;">Participant Information</h6>
                    <p style="color: #000 !important;"><strong>Name:</strong> ${selectedFeedback.name || 'Anonymous'}</p>
                    <p style="color: #000 !important;"><strong>Phone:</strong> ${selectedFeedback.phone_number || 'Not provided'}</p>
                    <p style="color: #000 !important;"><strong>Submitted:</strong> ${new Date(selectedFeedback.created_at).toLocaleString()}</p>
                </div>
                <div class="col-md-6">
                    <h6 style="color: #000 !important;">Ratings</h6>
                    <p style="color: #000 !important;"><strong>Benefit Rating:</strong> ${selectedFeedback.benefit_rating}/10</p>
                    <p style="color: #000 !important;"><strong>Speaker Rating:</strong> ${selectedFeedback.speaker_rating}/5</p>
                    <p style="color: #000 !important;"><strong>Would Recommend:</strong> ${selectedFeedback.would_recommend ? 'Yes' : 'No'}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <h6 style="color: #000 !important;">Most Helpful Topic</h6>
                    <p style="color: #000 !important;">${selectedFeedback.most_helpful_topic || 'Not provided'}</p>
                </div>
            </div>
        `;
        $('#feedbackModal').modal('show');
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/scientist/feedback/feedback-details.blade.php ENDPATH**/ ?>