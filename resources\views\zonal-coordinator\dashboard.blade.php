@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('Zonal/State Coordinator/Facilitator Dashboard') }}</div>

                <div class="card-body">
                    <h2 class="text-center mb-4">Welcome Zonal/State Coordinator/Facilitator</h2>

                    <div class="alert alert-success" role="alert">
                        You are logged in as a Zonal/State Coordinator/Facilitator!
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Districts</h5>
                                    <p class="card-text display-4">{{ $stats['districts_count'] ?? 0 }}</p>
                                    <p class="card-text">
                                        <small>
                                            Pre-Cocoon: {{ $stats['pre_cocoon_districts'] ?? 0 }} |
                                            Post-Cocoon: {{ $stats['post_cocoon_districts'] ?? 0 }}
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Scientists</h5>
                                    <p class="card-text display-4">{{ $stats['scientists_count'] ?? 0 }}</p>
                                    <p class="card-text">
                                        <small>
                                            Feedback Provided: {{ $stats['feedback_count'] ?? 0 }}
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Action Plans</h5>
                                    <p class="card-text display-4">{{ $stats['action_plans_count'] ?? 0 }}</p>
                                    <p class="card-text">
                                        <small>
                                            Completed: {{ $stats['completed_action_plans'] ?? 0 }}
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        {{-- <div class="col-md-3">
                            <div class="card bg-warning text-dark mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Your Visits</h5>
                                    <p class="card-text display-4">{{ $stats['visits_count'] ?? 0 }}</p>
                                    <p class="card-text">
                                        <small>
                                            Completed: {{ $stats['completed_visits'] ?? 0 }}
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div> --}}

                    </div>

                    <!-- Quick Access Cards -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">My Districts</div>
                                <div class="card-body">
                                    <p>View and manage your assigned districts.</p>
                                    <a href="{{ route('zonal-coordinator.districts.index') }}" class="btn btn-primary">View Districts</a>
                                </div>
                            </div>
                        </div>
                        {{-- <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">Scientists</div>
                                <div class="card-body">
                                    <p>View scientists in your assigned districts.</p>
                                    <a href="{{ route('zonal-coordinator.scientists.index') }}" class="btn btn-primary">View Scientists</a>
                                </div>
                            </div>
                        </div> --}}
                        {{-- <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">Visit Planning</div>
                                <div class="card-body">
                                    <p>Plan and manage your district visits.</p>
                                    <a href="{{ route('zonal-coordinator.visits.index') }}" class="btn btn-primary">Manage Visits</a>
                                </div>
                            </div>
                        </div> --}}

                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header">Participant Feedback</div>
                                <div class="card-body">
                                    <p>Collect and manage feedback from event participants.</p>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('zonal-coordinator.feedback-import.index') }}" class="btn btn-primary me-2">Manage Feedback</a>
                                        {{-- <a href="{{ route('zonal-coordinator.feedback-analytics.index') }}" class="btn btn-success">Analytics Dashboard</a> --}}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">Scientist & Action Plan Feedback</div>
                                <div class="card-body">
                                    <p>Provide feedback to scientists & Action plans.</p>
                                    <a href="{{ route('zonal-coordinator.scientist-feedback.index') }}" class="btn btn-primary">Manage Feedback</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Action Plans -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Recent Action Plans</div>
                                <div class="card-body">
                                    @if(isset($recentActionPlans) && count($recentActionPlans) > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Scientist</th>
                                                        <th>District</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($recentActionPlans as $plan)
                                                        <tr>
                                                            <td>{{ $plan->title }}</td>
                                                            <td>{{ $plan->scientist->name ?? 'N/A' }}</td>
                                                            <td>{{ $plan->district->district ?? 'N/A' }}</td>
                                                            <td>
                                                                @if($plan->status == 'planned')
                                                                    <span class="badge bg-warning">Planned</span>
                                                                @elseif($plan->status == 'completed')
                                                                    <span class="badge bg-success">Completed</span>
                                                                @else
                                                                    <span class="badge bg-danger">Cancelled</span>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <a href="{{ route('zonal-coordinator.events.index') }}" class="btn btn-sm btn-primary">View All Events</a>
                                    @else
                                        <p class="text-center">No recent action plans found.</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        {{-- <!-- Recent Visits -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Your Recent Visits</div>
                                <div class="card-body">
                                    @if(isset($recentVisits) && count($recentVisits) > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>District</th>
                                                        <th>Date</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($recentVisits as $visit)
                                                        <tr>
                                                            <td>{{ $visit->title }}</td>
                                                            <td>{{ $visit->district->district ?? 'N/A' }}</td>
                                                            <td>{{ \Carbon\Carbon::parse($visit->visit_date)->format('d M Y') }}</td>
                                                            <td>
                                                                @if($visit->status == 'planned')
                                                                    <span class="badge bg-warning">Planned</span>
                                                                @elseif($visit->status == 'completed')
                                                                    <span class="badge bg-success">Completed</span>
                                                                @else
                                                                    <span class="badge bg-danger">Cancelled</span>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <a href="{{ route('zonal-coordinator.visits.index') }}" class="btn btn-sm btn-primary">View All Visits</a>
                                    @else
                                        <p class="text-center">No recent visits found.</p>
                                    @endif
                                </div>
                            </div>
                        </div> --}}
                    </div>

                    <!-- Participant Feedback Management -->
                    <div class="row mt-4">
                        {{-- <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Participant Feedback</div>
                                <div class="card-body">
                                    <p>Collect and manage feedback from event participants.</p>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('zonal-coordinator.feedback-import.index') }}" class="btn btn-primary me-2">Manage Feedback</a>
                                        {{-- <a href="{{ route('zonal-coordinator.feedback-analytics.index') }}" class="btn btn-success">Analytics Dashboard</a> --}}
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                        {{-- <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Performance Overview</div>
                                <div class="card-body">
                                    <p>View detailed performance metrics for your zone.</p>
                                    <a href="{{ route('zonal-coordinator.performance.index') }}" class="btn btn-primary">View Performance Dashboard</a>
                                </div>
                            </div>
                        </div> --}}
                    </div>

                    <!-- Notifications Management -->
                    {{-- <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Notification Settings</div>
                                <div class="card-body">
                                    <p>Configure email notifications and alerts for feedback.</p>
                                    <a href="{{ route('zonal-coordinator.notifications.index') }}" class="btn btn-primary">Manage Notifications</a>
                                </div>
                            </div>
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
