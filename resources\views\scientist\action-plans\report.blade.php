@extends('layouts.app')

@section('content')
<style>
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        margin-bottom: 1rem;
    }

    .rating input {
        display: none;
    }

    .rating label {
        cursor: pointer;
        width: 30px;
        height: 30px;
        margin: 0 2px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ddd"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>') no-repeat center center;
        background-size: contain;
        transition: transform 0.2s ease;
    }

    .rating input:checked ~ label,
    .rating label:hover,
    .rating label:hover ~ label {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffd700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
    }

    .rating label:hover,
    .rating label:hover ~ label {
        transform: scale(1.1);
    }
</style>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Submit Event Report</h5>
                </div>

                <div class="card-body">
                    <form id="reportForm" method="POST" action="{{ route('scientist.action-plans.report.submit', $actionPlan->id) }}">
                        @csrf

                        <div class="mb-3">
                            <label class="form-label">Event Success Rating</label>
                            <div class="rating">
                                <input type="radio" id="success_rating_5" name="scientist_event_success_rating" value="5" required>
                                <label for="success_rating_5" title="5 stars"></label>
                                <input type="radio" id="success_rating_4" name="scientist_event_success_rating" value="4">
                                <label for="success_rating_4" title="4 stars"></label>
                                <input type="radio" id="success_rating_3" name="scientist_event_success_rating" value="3">
                                <label for="success_rating_3" title="3 stars"></label>
                                <input type="radio" id="success_rating_2" name="scientist_event_success_rating" value="2">
                                <label for="success_rating_2" title="2 stars"></label>
                                <input type="radio" id="success_rating_1" name="scientist_event_success_rating" value="1">
                                <label for="success_rating_1" title="1 star"></label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_challenges_faced" class="form-label">Challenges Faced</label>
                            <textarea class="form-control" id="scientist_challenges_faced" name="scientist_challenges_faced" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_improvement_suggestions" class="form-label">Improvement Suggestions</label>
                            <textarea class="form-control" id="scientist_improvement_suggestions" name="scientist_improvement_suggestions" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_remarks" class="form-label">Additional Remarks</label>
                            <textarea class="form-control" id="scientist_remarks" name="scientist_remarks" rows="3"></textarea>
                        </div>

                        <!-- VIP Participation Section -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">VIP Participation</h6>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="vip_participated" name="vip_participated" value="1">
                                    <label class="form-check-label" for="vip_participated">
                                        Did any VIP participate in your event?
                                    </label>
                                </div>
                            </div>

                            <div id="vip_details" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vip_mla_mp" name="vip_mla_mp" value="1">
                                            <label class="form-check-label" for="vip_mla_mp">MLA/MP</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vip_ms_jst_director" name="vip_ms_jst_director" value="1">
                                            <label class="form-check-label" for="vip_ms_jst_director">MS/JST/Director</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vip_local_pr" name="vip_local_pr" value="1">
                                            <label class="form-check-label" for="vip_local_pr">Local PR</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vip_state_official" name="vip_state_official" value="1">
                                            <label class="form-check-label" for="vip_state_official">State Official/DM/DC/Commissioner</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vip_others" name="vip_others" value="1">
                                            <label class="form-check-label" for="vip_others">Others</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3" id="vip_others_details_div" style="display: none;">
                                    <label for="vip_others_details" class="form-label">Please specify other VIPs:</label>
                                    <textarea class="form-control" id="vip_others_details" name="vip_others_details" rows="2" placeholder="Please provide details about other VIPs who participated"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Media Coverage Section -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Press/Media Coverage</h6>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="media_coverage" name="media_coverage" value="1">
                                    <label class="form-check-label" for="media_coverage">
                                        Any Press/Media Coverage?
                                    </label>
                                </div>
                            </div>

                            <div id="media_details" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="media_newspaper" name="media_newspaper" value="1">
                                            <label class="form-check-label" for="media_newspaper">Newspaper</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="media_tv_news" name="media_tv_news" value="1">
                                            <label class="form-check-label" for="media_tv_news">TV News</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="media_radio" name="media_radio" value="1">
                                            <label class="form-check-label" for="media_radio">Radio</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="media_links" class="form-label">Media Links</label>
                                    <textarea class="form-control" id="media_links" name="media_links" rows="3" placeholder="Please paste links to media coverage (one per line)"></textarea>
                                    <div class="form-text">Enter one link per line</div>
                                </div>

                                <div class="mb-3">
                                    <label for="media_photo" class="form-label">Media Coverage Photo</label>
                                    <input type="file" class="form-control" id="media_photo" name="media_photo" accept="image/*">
                                    <div class="form-text">Upload a photo related to media coverage (optional)</div>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media Section -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Social Media Post Links</h6>
                            <div class="mb-3">
                                <label for="social_media_links" class="form-label">Social Media Post Links</label>
                                <textarea class="form-control" id="social_media_links" name="social_media_links" rows="3" placeholder="Please paste links to social media posts about the event (one per line)"></textarea>
                                <div class="form-text">Enter one link per line (Facebook, Twitter, Instagram, LinkedIn, etc.)</div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Submit Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const reportForm = document.getElementById('reportForm');

        // Handle VIP participation toggle
        const vipParticipated = document.getElementById('vip_participated');
        const vipDetails = document.getElementById('vip_details');
        const vipOthers = document.getElementById('vip_others');
        const vipOthersDetailsDiv = document.getElementById('vip_others_details_div');

        vipParticipated.addEventListener('change', function() {
            if (this.checked) {
                vipDetails.style.display = 'block';
            } else {
                vipDetails.style.display = 'none';
                // Uncheck all VIP checkboxes when main checkbox is unchecked
                document.querySelectorAll('#vip_details input[type="checkbox"]').forEach(cb => cb.checked = false);
                vipOthersDetailsDiv.style.display = 'none';
            }
        });

        vipOthers.addEventListener('change', function() {
            if (this.checked) {
                vipOthersDetailsDiv.style.display = 'block';
            } else {
                vipOthersDetailsDiv.style.display = 'none';
                document.getElementById('vip_others_details').value = '';
            }
        });

        // Handle media coverage toggle
        const mediaCoverage = document.getElementById('media_coverage');
        const mediaDetails = document.getElementById('media_details');

        mediaCoverage.addEventListener('change', function() {
            if (this.checked) {
                mediaDetails.style.display = 'block';
            } else {
                mediaDetails.style.display = 'none';
                // Uncheck all media checkboxes when main checkbox is unchecked
                document.querySelectorAll('#media_details input[type="checkbox"]').forEach(cb => cb.checked = false);
                document.getElementById('media_links').value = '';
                document.getElementById('media_photo').value = '';
            }
        });

        reportForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(reportForm);

            // Handle media links and social media links as arrays
            const mediaLinksText = document.getElementById('media_links').value;
            const socialMediaLinksText = document.getElementById('social_media_links').value;

            // Convert text areas to arrays (split by newlines and filter empty lines)
            const mediaLinksArray = mediaLinksText ? mediaLinksText.split('\n').filter(link => link.trim() !== '') : [];
            const socialMediaLinksArray = socialMediaLinksText ? socialMediaLinksText.split('\n').filter(link => link.trim() !== '') : [];

            // Set the arrays in formData
            formData.set('media_links', JSON.stringify(mediaLinksArray));
            formData.set('social_media_links', JSON.stringify(socialMediaLinksArray));

            fetch(reportForm.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    if (typeof data.error === 'object') {
                        const errorMessages = Object.values(data.error).flat();
                        alert(errorMessages.join('\n'));
                    } else {
                        alert(data.error);
                    }
                    return;
                }

                alert(data.message || 'Report submitted successfully');
                window.location.href = '{{ route("scientist.action-plans.index") }}';
            })
            .catch(error => {
                console.error('Error submitting report:', error);
                alert('Error submitting report. Please try again later.');
            });
        });
    });
</script>
@endpush

@endsection
