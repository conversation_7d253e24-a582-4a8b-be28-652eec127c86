<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\StateData;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DistrictUserController extends Controller
{
    /**
     * Display the district user management page.
     */
    public function index()
    {
        return view('admin.district-user.index');
    }

    /**
     * Get all unique states from the JSON file.
     */
    public function getStates()
    {
        Log::info('Getting all states from JSON file');

        try {
            // Read the JSON file directly without using Storage facade
            $jsonContent = file_get_contents(storage_path('app/states_districts.json'));

            // Try to fix common JSON errors before parsing
            $jsonContent = preg_replace('/,\s*}/', '}', $jsonContent); // Remove trailing commas in objects
            $jsonContent = preg_replace('/,\s*]/', ']', $jsonContent); // Remove trailing commas in arrays

            // Fix specific issues we know about
            $jsonContent = str_replace('"id": 92, "Sondheim": "name":', '"id": 92, "name":', $jsonContent);
            $jsonContent = str_replace('"id": 284, "name "Nainital"', '"id": 284, "name": "Nainital"', $jsonContent);

            $data = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON parsing error: ' . json_last_error_msg());
                // Dump the content for debugging
                Log::error('JSON content: ' . substr($jsonContent, 0, 200) . '...');
                return response()->json([]);
            }

            if (!$data || !isset($data['states'])) {
                Log::error('Invalid JSON structure in states_districts.json');
                return response()->json([]);
            }

            // Extract state names
            $states = collect($data['states'])->pluck('name')->toArray();

            Log::info('Found ' . count($states) . ' states from JSON file');
            return response()->json($states);
        } catch (\Exception $e) {
            Log::error('Error reading states from JSON file: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Get districts by state from the JSON file.
     */
    public function getDistrictsByState($state)
    {
        Log::info('Getting districts for state: ' . $state . ' from JSON file');

        try {
            // Read the JSON file directly without using Storage facade
            $jsonContent = file_get_contents(storage_path('app/states_districts.json'));

            // Try to fix common JSON errors before parsing
            $jsonContent = preg_replace('/,\s*}/', '}', $jsonContent); // Remove trailing commas in objects
            $jsonContent = preg_replace('/,\s*]/', ']', $jsonContent); // Remove trailing commas in arrays

            // Fix specific issues we know about
            $jsonContent = str_replace('"id": 92, "Sondheim": "name":', '"id": 92, "name":', $jsonContent);
            $jsonContent = str_replace('"id": 284, "name "Nainital"', '"id": 284, "name": "Nainital"', $jsonContent);

            $data = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON parsing error: ' . json_last_error_msg());
                // Dump the content for debugging
                Log::error('JSON content: ' . substr($jsonContent, 0, 200) . '...');
                return response()->json([]);
            }

            if (!$data || !isset($data['states'])) {
                Log::error('Invalid JSON structure in states_districts.json');
                return response()->json([]);
            }

            // Find the state in the JSON data
            $stateData = collect($data['states'])->firstWhere('name', $state);

            if (!$stateData || !isset($stateData['districts'])) {
                Log::error('State not found or no districts available for state: ' . $state);
                return response()->json([]);
            }

            // Get districts for the state - don't rely on database
            $districts = collect($stateData['districts'])->map(function($district) {
                // Try to get district data from state_data table, but don't fail if not found
                try {
                    $stateData = StateData::where('id', $district['id'])->first();
                } catch (\Exception $ex) {
                    Log::error('Error finding district in state_data table: ' . $ex->getMessage());
                    $stateData = null;
                }

                // Try to get scientist name, but don't fail if not found
                $scientistName = null;
                if ($stateData && $stateData->scientist) {
                    try {
                        $scientistName = User::where('email', $stateData->scientist)->value('name');
                    } catch (\Exception $ex) {
                        Log::error('Error finding scientist name: ' . $ex->getMessage());
                        $scientistName = null;
                    }
                }

                // Get status and scientist email from state_data table only
                return [
                    'id' => $district['id'],
                    'name' => $district['name'],
                    'status' => $stateData ? $stateData->status : null,
                    'scientist' => $stateData ? $stateData->scientist : null,
                    'scientist_name' => $scientistName
                ];
            });

            Log::info('Found ' . count($districts) . ' districts for state: ' . $state);
            return response()->json($districts);
        } catch (\Exception $e) {
            Log::error('Error reading districts from JSON file: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Get district details including scientist info.
     */
    public function getDistrictDetails($id)
    {
        Log::info('Getting district details for ID: ' . $id);

        try {
            // Try to get district data from the state_data table, but don't fail if not found
            try {
                $stateData = StateData::where('id', $id)->first();
                if ($stateData) {
                    $district = new \stdClass();
                    $district->id = $stateData->id;
                    $district->state = $stateData->state;
                    $district->district = $stateData->district;
                    $district->status = $stateData->status;
                    $district->scientist = $stateData->scientist;
                } else {
                    $district = null;
                }
            } catch (\Exception $ex) {
                $district = null;
                Log::error('Error finding district in state_data table: ' . $ex->getMessage());
            }

            // If not found in database, try to find in JSON file
            if (!$district) {
                // Read the JSON file directly without using Storage facade
                $jsonContent = file_get_contents(storage_path('app/states_districts.json'));

                // Try to fix common JSON errors before parsing
                $jsonContent = preg_replace('/,\s*}/', '}', $jsonContent); // Remove trailing commas in objects
                $jsonContent = preg_replace('/,\s*]/', ']', $jsonContent); // Remove trailing commas in arrays

                // Fix specific issues we know about
                $jsonContent = str_replace('"id": 92, "Sondheim": "name":', '"id": 92, "name":', $jsonContent);
                $jsonContent = str_replace('"id": 284, "name "Nainital"', '"id": 284, "name": "Nainital"', $jsonContent);

                $data = json_decode($jsonContent, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON parsing error: ' . json_last_error_msg());
                    // Dump the content for debugging
                    Log::error('JSON content: ' . substr($jsonContent, 0, 200) . '...');
                    return response()->json(['error' => 'Failed to parse JSON data'], 500);
                }

                if (!$data || !isset($data['states'])) {
                    Log::error('Invalid JSON structure in states_districts.json');
                    return response()->json(['error' => 'District not found'], 404);
                }

                // Search for the district in all states
                $districtData = null;
                $stateData = null;

                foreach ($data['states'] as $state) {
                    foreach ($state['districts'] as $dist) {
                        if ($dist['id'] == $id) {
                            $districtData = $dist;
                            $stateData = $state;
                            break 2;
                        }
                    }
                }

                if (!$districtData) {
                    Log::error('District with ID ' . $id . ' not found in JSON file');
                    return response()->json(['error' => 'District not found'], 404);
                }

                // Create a district object with the data from JSON
                $district = new \stdClass();
                $district->id = $districtData['id'];
                $district->state = $stateData['name'];
                $district->district = $districtData['name'];
                $district->status = null;
                $district->scientist = null;
            }

            $scientist = null;
            if (isset($district->scientist) && $district->scientist) {
                try {
                    $scientist = User::where('email', $district->scientist)
                        ->select('id', 'name', 'email', 'phone_number')
                        ->first();
                } catch (\Exception $ex) {
                    $scientist = null;
                    Log::error('Error finding scientist: ' . $ex->getMessage());
                }
            }

            return response()->json([
                'district' => $district,
                'scientist' => $scientist
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting district details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get district details'], 500);
        }
    }

    /**
     * Save district type and scientist info.
     */
    public function saveDistrictUser(Request $request)
    {
        $request->validate([
            'district_id' => 'required',
            'user_id' => 'nullable|integer|exists:users,id',
            'state_name' => 'required|string|max:100',
            'district_name' => 'required|string|max:255',
            'status' => 'required|string|in:Pre-Cocoon,Post-Cocoon',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'password' => 'required_if:is_new,true|nullable|string|min:4',
        ]);

        // Start a transaction
        DB::beginTransaction();

        try {
            // Try to find district in state_data table, but don't fail if not found
            try {
                $stateData = StateData::where('id', $request->district_id)->first();
            } catch (\Exception $ex) {
                Log::error('Error finding district in state_data table: ' . $ex->getMessage());
                $stateData = null;
            }

            if (!$stateData) {
                try {
                    // Create new state_data record if it doesn't exist
                    $stateData = new StateData();
                    $stateData->id = $request->district_id;
                    $stateData->state = $request->state_name;
                    $stateData->district = $request->district_name;
                } catch (\Exception $ex) {
                    Log::error('Error creating state_data record: ' . $ex->getMessage());
                    throw $ex; // Re-throw to be caught by outer try-catch
                }
            }

            // Update district type and scientist
            $stateData->status = $request->status;
            $stateData->scientist = $request->email;

            try {
                $stateData->save();
            } catch (\Exception $ex) {
                Log::error('Error saving state_data record: ' . $ex->getMessage());
                throw $ex; // Re-throw to be caught by outer try-catch
            }

            $user = null;

            // If user_id is provided, find user by ID (for updates)
            if ($request->filled('user_id')) {
                try {
                    $user = User::find($request->user_id);

                    // Check if email is being changed to an existing email
                    if ($user && $user->email !== $request->email) {
                        $existingEmailUser = User::where('email', $request->email)
                            ->where('id', '!=', $user->id)
                            ->first();

                        if ($existingEmailUser) {
                            return response()->json([
                                'success' => false,
                                'message' => 'Email already exists for another user'
                            ], 422);
                        }
                    }
                } catch (\Exception $ex) {
                    Log::error('Error finding user by ID: ' . $ex->getMessage());
                    $user = null;
                }
            } else {
                // For new users, check if email already exists
                try {
                    $existingEmailUser = User::where('email', $request->email)->first();
                    if ($existingEmailUser) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Email already exists'
                        ], 422);
                    }
                } catch (\Exception $ex) {
                    Log::error('Error checking existing email: ' . $ex->getMessage());
                }
            }

            if ($user) {
                // Store old email to update district assignments if email changes
                $oldEmail = $user->email;

                // Update existing user
                $user->name = $request->name;
                $user->email = $request->email;
                $user->phone_number = $request->phone_number;

                // Update password if provided
                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                }

                try {
                    $user->save();

                    // If email changed, update district assignments in state_data table
                    if ($oldEmail !== $request->email) {
                        \DB::table('state_data')
                            ->where('scientist', $oldEmail)
                            ->update(['scientist' => $request->email]);

                        Log::info('Updated district assignments for scientist email change', [
                            'old_email' => $oldEmail,
                            'new_email' => $request->email
                        ]);
                    }
                } catch (\Exception $ex) {
                    Log::error('Error updating user: ' . $ex->getMessage());
                    throw $ex; // Re-throw to be caught by outer try-catch
                }
            } else {
                // Create new scientist user
                try {
                    User::create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'phone_number' => $request->phone_number,
                        'password' => Hash::make($request->password),
                        'role' => 'scientist',
                    ]);
                } catch (\Exception $ex) {
                    Log::error('Error creating user: ' . $ex->getMessage());
                    throw $ex; // Re-throw to be caught by outer try-catch
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'District user saved successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error saving district user: ' . $e->getMessage()
            ], 500);
        }
    }


}
