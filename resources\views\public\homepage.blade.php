@extends('layouts.public-layout')

@section('title', 'Home - Mera <PERSON>sham Mera Abhimaan')

@section('content')
<!-- Hero Banner Section -->
<div class="banner-slider relative">
    <div class="relative overflow-hidden" style="height: 500px;">
        <img src="{{ asset('images/banner_1.png') }}" alt="Mera Resham Mera Abhimaan Banner"
             class="w-full h-full object-cover">
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white px-4">
                <h1 class="text-4xl md:text-6xl font-bold mb-4 drop-shadow-lg">
                    Mera Resham Mera Abhimaan
                </h1>
                <p class="text-xl md:text-2xl mb-8 drop-shadow-lg">
                    A Sericulture Technology Transfer Campaign
                </p>
            </div>
        </div>
    </div>
</div>

<!-- About MRMA Section -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title text-3xl font-bold mb-4">About Mera Resham Mera Abhimaan (MRMA)</h2>
            <p class="text-lg text-gray-600 max-w-4xl mx-auto">
                A sericulture technology transfer campaign.
            </p>
            <p class="text-lg text-gray-600 max-w-4xl mx-auto">
                The Central Silk Board has initiated “Mera Resham Mera Abhimaan” (MRMA) a mega campaign. This mega campaign is an ambitious technology transfer programme for effective transfer of advanced sericulture technologies by bridging the research–field gap through a District Adoption model, by proactive and collaborative engagement of scientists and technical staff from the Central Silk Board (CSB) and State Department of Sericulture (DoS).
            </p>
        </div>



        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mt-16">
            <div>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-blue-100 p-3 rounded-full mr-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">The Challenge</h3>
                    </div>
                    <p class="text-gray-700 leading-relaxed">
                        Despite advanced technologies developed by the Central Silk Board, sericulture farmers continue to rely on outdated practices due to inadequate awareness and limited access to modern technologies. A significant gap exists between research institutions and grassroots-level stakeholders.
                    </p>
                </div>
            </div>

            <div>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-green-100 p-3 rounded-full mr-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">The Solution</h3>
                    </div>
                    <p class="text-gray-700 leading-relaxed">
                        MRMA is an ambitious technology transfer programme that bridges the research–field gap through a District Adoption model, with proactive collaboration between CSB scientists and State Department of Sericulture officials.
                    </p>
                </div>
            </div>
        </div>

        <!-- Mission Statement -->
        <div class="mt-16 text-center">
            <div class="bg-orange-50 rounded-lg p-8 border-l-4 border-orange-500">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                <p class="text-lg text-gray-700 italic">
                    "To enhance silk production and productivity and also for the socio-economic development of  sericulture farmers and to make India as a global leader in silk."
                </p>
                <p class="text-sm text-gray-600 mt-4">
                    Launched by Shri Giriraj Singh, Hon'ble Minister of Textiles, Government of India
                </p>
            </div>
        </div>


        <!-- Campaign Highlights -->
        <div class="mt-16">
            <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-8 text-white">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold mb-4">Campaign Highlights</h3>
                    <p class="text-blue-100">A 100-day mega campaign transforming sericulture across India</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">120+</div>
                        <div class="text-blue-100">Potential Districts</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">26</div>
                        <div class="text-blue-100">Silk-Producing States</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">1 Lakh+</div>
                        <div class="text-blue-100">Farmers to be Reached</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold mb-2">1000+</div>
                        <div class="text-blue-100">Training Programmes</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<!-- Platform Statistics Section -->
<div class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title text-3xl font-bold mb-4">Platform Statistics</h2>
            <p class="text-lg text-gray-600">Real-time data from our comprehensive sericulture platform</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">{{ number_format($stats['total_users']) }}</div>
                <div class="text-gray-600">Total Users</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-green-600 mb-2">{{ number_format($stats['scientists']) }}</div>
                <div class="text-gray-600">Scientists</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-purple-600 mb-2">{{ number_format($stats['districts']) }}</div>
                <div class="text-gray-600">Districts</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-orange-600 mb-2">{{ number_format($stats['states']) }}</div>
                <div class="text-gray-600">States</div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-red-600 mb-2">{{ number_format($stats['action_plans']) }}</div>
                <div class="text-gray-600">No. of Programs Planned</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-indigo-600 mb-2">{{ number_format($stats['completed_action_plans']) }}</div>
                <div class="text-gray-600">No. of Programs Completed</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-pink-600 mb-2">{{ number_format($stats['total_participants']) }}</div>
                <div class="text-gray-600">Participants</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-4xl font-bold text-teal-600 mb-2">{{ number_format($stats['feedback_count']) }}</div>
                <div class="text-gray-600">Feedback Received</div>
            </div>
        </div>

        <!-- District Classification -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-white rounded-lg shadow-md p-8">
                <div class="flex items-center mb-6">
                    <div class="bg-blue-100 p-3 rounded-full mr-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-blue-600">Pre-Cocoon Districts</h3>
                </div>
                <div class="text-center">
                    <div class="text-5xl font-bold text-blue-600 mb-4">{{ number_format($stats['pre_cocoon_districts']) }}</div>
                    <p class="text-gray-700">Districts focusing on Mulburry, Tasar, Eri & Muga.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-8">
                <div class="flex items-center mb-6">
                    <div class="bg-green-100 p-3 rounded-full mr-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-green-600">Post-Cocoon Districts</h3>
                </div>
                <div class="text-center">
                    <div class="text-5xl font-bold text-green-600 mb-4">{{ number_format($stats['post_cocoon_districts']) }}</div>
                    <p class="text-gray-700">Districts focusing on silk reeling, weaving and value addition</p>
                </div>
            </div>
        </div>
    </div>
</div>






<!-- Upcoming Action Plans This Week Section -->
@if($upcomingActionPlans->count() > 0)
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title text-3xl font-bold mb-4 text-gray-900">Upcoming Programs This Week</h2>
            <p class="text-lg text-gray-600 max-w-4xl mx-auto">
                Join us for these exciting sericulture programs happening this week across different districts
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($upcomingActionPlans as $actionPlan)
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="p-6">
                    <!-- Program Type Badge -->
                    <div class="flex items-center justify-between mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            @if($actionPlan->type === 'training') bg-blue-100 text-blue-800
                            @elseif($actionPlan->type === 'field_visit') bg-green-100 text-green-800
                            @elseif($actionPlan->type === 'demonstration') bg-purple-100 text-purple-800
                            @else bg-orange-100 text-orange-800
                            @endif">
                            {{ ucfirst(str_replace('_', ' ', $actionPlan->type)) }}
                        </span>
                        <span class="text-sm text-gray-500">
                            {{ \Carbon\Carbon::parse($actionPlan->planned_date)->format('M d, Y') }}
                        </span>
                    </div>

                    <!-- Program Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3">{{ $actionPlan->title }}</h3>

                    <!-- Location -->
                    <div class="flex items-center text-gray-600 mb-3">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>{{ $actionPlan->location }}</span>
                    </div>

                    <!-- District -->
                    <div class="flex items-center text-gray-600 mb-3">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                        </svg>
                        <span>{{ $actionPlan->district->district ?? 'N/A' }}, {{ $actionPlan->district->state ?? 'N/A' }}</span>
                    </div>

                    <!-- Scientist -->
                    <div class="flex items-center text-gray-600 mb-4">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>{{ $actionPlan->scientist->name ?? 'N/A' }}</span>
                    </div>

                    <!-- Expected Participants -->
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Expected Participants: {{ $actionPlan->expected_participants ?? 'TBD' }}</span>
                        <span class="text-xs">
                            {{ \Carbon\Carbon::parse($actionPlan->planned_date)->format('l') }}
                        </span>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- View All Programs Link -->
        <div class="text-center mt-12">
            <p class="text-gray-600 mb-4">Want to participate or provide feedback on these programs?</p>
            @if($totalUpcomingCount > 6)
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Showing 6 of {{ $totalUpcomingCount }} programs this week</p>
                </div>
                <a href="{{ route('public.upcoming-programs') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                    View All {{ $totalUpcomingCount }} Programs
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            @else
                <a href="{{ route('public.upcoming-programs') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                    Learn More About Our Programs
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            @endif
        </div>
    </div>
</div>
@endif
<!-- MRMA Coverage Map Section -->
<div class="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title text-3xl font-bold mb-4 text-gray-900">MRMA Coverage Across India</h2>
            <p class="text-lg text-gray-600 max-w-4xl mx-auto">
                Comprehensive coverage of districts under MRMA and silk clusters across the nation
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-xl overflow-hidden">
            <div class="p-8">
                <!-- Map Image -->
                <div class="text-center mb-8">
                    <img src="{{ asset('images/WhatsApp Image 2025-06-16 at 17.39.11.jpeg') }}"
                         alt="MRMA Coverage Map - Districts under MRMA and Silk Clusters across India"
                         class="w-full max-w-4xl mx-auto rounded-lg shadow-lg border border-gray-200">
                </div>

                <!-- Map Legend -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">Map Legend</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                        <div class="flex items-center justify-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3 border-2 border-green-600"></div>
                            <span class="text-gray-700 font-medium">Districts Under MRMA</span>
                        </div>
                        <div class="flex items-center justify-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-3 border-2 border-blue-600"></div>
                            <span class="text-gray-700 font-medium">Silk Clusters</span>
                        </div>
                    </div>
                </div>

                <!-- Map Description -->
                <div class="mt-8 text-center">
                    <p class="text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        This comprehensive map showcases the extensive reach of the Mera Resham Mera Abhimaan campaign across India.
                        The green markers indicate districts actively participating in MRMA initiatives, while blue markers represent
                        established silk clusters contributing to India's rich sericulture heritage.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>











<!-- Statistics Section -->
<div class="py-16 bg-blue-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Our Impact</h2>
            <p class="text-lg text-blue-100">Making a difference in the lives of silk farmers across India</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">{{ number_format($stats['total_participants']) }}+</div>
                <div class="text-blue-100">Participants Reached</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">{{ number_format($stats['completed_action_plans']) }}+</div>
                <div class="text-blue-100">Training Programs</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">{{ number_format($stats['states']) }}</div>
                <div class="text-blue-100">States Covered</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">{{ number_format($stats['scientists']) }}+</div>
                <div class="text-blue-100">Scientists Active</div>
            </div>
        </div>
    </div>
</div>
@endsection
