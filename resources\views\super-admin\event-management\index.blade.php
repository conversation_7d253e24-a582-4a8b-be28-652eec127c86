@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Event Management') }}</h5>
                    <div>
                        <button id="addEventBtn" class="btn btn-sm btn-success me-2">Add New Event</button>
                        <a href="{{ route('super-admin.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Manage events conducted by scientists in different districts.</li>
                            <li>Track event details, participation metrics, and outcomes.</li>
                            <li>Events can be in one of three states: Planned, Completed, or Cancelled.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>District</th>
                                    <th>Scientist</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- Events will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Event Form Modal -->
                    <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="eventModalLabel">Add New Event</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="eventForm">
                                        <input type="hidden" id="eventId" name="id">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="title">Event Title:</label>
                                                    <input type="text" class="form-control" id="title" name="title" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="topic">Topic:</label>
                                                    <input type="text" class="form-control" id="topic" name="topic" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group mb-3">
                                                    <label for="description">Description:</label>
                                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="start_date">Start Date & Time:</label>
                                                    <input type="datetime-local" class="form-control" id="start_date" name="start_date" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="end_date">End Date & Time:</label>
                                                    <input type="datetime-local" class="form-control" id="end_date" name="end_date" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="location">Location:</label>
                                                    <input type="text" class="form-control" id="location" name="location" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="status">Status:</label>
                                                    <select class="form-control" id="status" name="status" required>
                                                        <option value="planned">Planned</option>
                                                        <option value="completed">Completed</option>
                                                        <option value="cancelled">Cancelled</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="district_id">District:</label>
                                                    <select class="form-control" id="district_id" name="district_id" required>
                                                        <option value="">-- Select District --</option>
                                                        <!-- Districts will be loaded here -->
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="scientist_id">Scientist:</label>
                                                    <select class="form-control" id="scientist_id" name="scientist_id" required>
                                                        <option value="">-- Select Scientist --</option>
                                                        <!-- Scientists will be loaded here -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="expected_participants">Expected Participants:</label>
                                                    <input type="number" class="form-control" id="expected_participants" name="expected_participants" min="1" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="actual_participants">Actual Participants:</label>
                                                    <input type="number" class="form-control" id="actual_participants" name="actual_participants" min="0">
                                                    <small class="form-text text-muted">Required for completed events.</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="demographicsSection" style="display: none;">
                                            <h5 class="mt-4 mb-3">Participant Demographics</h5>
                                            <h6 class="mb-2">Gender Distribution</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="male_participants">Male Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="male_participants" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="female_participants">Female Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="female_participants" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="transgender_participants">Transgender Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="transgender_participants" min="0">
                                                    </div>
                                                </div>
                                            </div>

                                            <h6 class="mb-2 mt-3">Caste Distribution</h6>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label for="st_participants">ST Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="st_participants" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label for="sc_participants">SC Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="sc_participants" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label for="obc_participants">OBC Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="obc_participants" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group mb-3">
                                                        <label for="general_participants">General Participants:</label>
                                                        <input type="number" class="form-control demographics-input" id="general_participants" min="0">
                                                    </div>
                                                </div>
                                            </div>

                                            <h6 class="mb-2 mt-3">Age Distribution</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="age_below_30">Below 30 Years:</label>
                                                        <input type="number" class="form-control demographics-input" id="age_below_30" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="age_30_to_50">30-50 Years:</label>
                                                        <input type="number" class="form-control demographics-input" id="age_30_to_50" min="0">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="age_above_50">Above 50 Years:</label>
                                                        <input type="number" class="form-control demographics-input" id="age_above_50" min="0">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="participant_demographics" name="participant_demographics">
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="saveEventBtn">Save Event</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this event? This action cannot be undone.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Event</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const eventsTableBody = document.getElementById('eventsTableBody');
        const addEventBtn = document.getElementById('addEventBtn');
        const eventForm = document.getElementById('eventForm');
        const saveEventBtn = document.getElementById('saveEventBtn');
        const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const statusSelect = document.getElementById('status');
        const actualParticipantsInput = document.getElementById('actual_participants');
        const demographicsSection = document.getElementById('demographicsSection');
        const demographicsInputs = document.querySelectorAll('.demographics-input');
        const participantDemographicsInput = document.getElementById('participant_demographics');
        let currentEventId = null;

        // Load events
        loadEvents();

        // Load districts and scientists
        loadDistricts();
        loadScientists();

        // Add event button click
        addEventBtn.addEventListener('click', function() {
            resetForm();
            document.getElementById('eventModalLabel').textContent = 'Add New Event';
            eventModal.show();
        });

        // Status change event
        statusSelect.addEventListener('change', function() {
            toggleDemographicsSection();
        });

        // Save event button click
        saveEventBtn.addEventListener('click', function() {
            if (!validateForm()) {
                return;
            }

            // Update participant demographics
            updateParticipantDemographics();

            const formData = new FormData(eventForm);
            const data = {};

            formData.forEach((value, key) => {
                if (key === 'participant_demographics' && value) {
                    data[key] = JSON.parse(value);
                } else {
                    data[key] = value;
                }
            });

            fetch('{{ route("super-admin.event-management.save") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    eventModal.hide();
                    loadEvents();
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving event:', error);
                alert('An error occurred while saving the event information.');
            });
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentEventId) {
                fetch(`{{ url("super-admin/event-management/delete") }}/${currentEventId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadEvents();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting event:', error);
                    alert('An error occurred while deleting the event.');
                });
            }
        });

        // Load events function
        function loadEvents() {
            fetch('{{ route("super-admin.event-management.events") }}')
                .then(response => response.json())
                .then(data => {
                    eventsTableBody.innerHTML = '';

                    data.forEach(event => {
                        const row = document.createElement('tr');

                        // Format date with improved error handling
                        let formattedDate = 'No Date';
                        if (event.start_date) {
                            try {
                                // Handle the date format from backend: "2025-11-06 15:33:00"
                                const startDate = new Date(event.start_date.replace(' ', 'T'));
                                if (!isNaN(startDate.getTime())) {
                                    formattedDate = startDate.toLocaleDateString('en-IN', {
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit'
                                    }) + ' ' + startDate.toLocaleTimeString('en-IN', {
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        hour12: true
                                    });
                                } else {
                                    console.warn('Invalid date for event:', event.title, 'Date value:', event.start_date);
                                    formattedDate = 'Invalid Date';
                                }
                            } catch (error) {
                                console.error('Date parsing error for event:', event.title, 'Error:', error, 'Date value:', event.start_date);
                                formattedDate = 'Date Error';
                            }
                        }

                        // Format status
                        let statusBadge = '';
                        if (event.status === 'planned') {
                            statusBadge = '<span class="badge bg-primary">Planned</span>';
                        } else if (event.status === 'completed') {
                            statusBadge = '<span class="badge bg-success">Completed</span>';
                        } else if (event.status === 'cancelled') {
                            statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                        }

                        // Format participants
                        let participants = `${event.actual_participants || 0} / ${event.expected_participants}`;

                        row.innerHTML = `
                            <td>${event.title}</td>
                            <td>${formattedDate}</td>
                            <td>${event.district ? event.district.district : ''} (${event.district ? event.district.state : ''})</td>
                            <td>${event.scientist ? event.scientist.name : ''}</td>
                            <td>${participants}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="${event.id}">Edit</button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${event.id}">Delete</button>
                            </td>
                        `;

                        eventsTableBody.appendChild(row);
                    });

                    // Add event listeners to edit buttons
                    document.querySelectorAll('.edit-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const eventId = this.dataset.id;
                            loadEventDetails(eventId);
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            currentEventId = this.dataset.id;
                            deleteConfirmModal.show();
                        });
                    });
                })
                .catch(error => console.error('Error loading events:', error));
        }

        // Load districts function
        function loadDistricts() {
            fetch('{{ route("super-admin.event-management.districts") }}')
                .then(response => response.json())
                .then(data => {
                    const districtSelect = document.getElementById('district_id');
                    districtSelect.innerHTML = '<option value="">-- Select District --</option>';

                    data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = `${district.district} (${district.state}) - ${district.status || 'No status'}`;
                        districtSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading districts:', error));
        }

        // Load scientists function
        function loadScientists() {
            fetch('{{ route("super-admin.event-management.scientists") }}')
                .then(response => response.json())
                .then(data => {
                    const scientistSelect = document.getElementById('scientist_id');
                    scientistSelect.innerHTML = '<option value="">-- Select Scientist --</option>';

                    data.forEach(scientist => {
                        const option = document.createElement('option');
                        option.value = scientist.id;
                        option.textContent = `${scientist.name} (${scientist.email})`;
                        scientistSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading scientists:', error));
        }

        // Load event details function
        function loadEventDetails(id) {
            fetch(`{{ url("super-admin/event-management/event") }}/${id}`)
                .then(response => response.json())
                .then(event => {
                    resetForm();

                    document.getElementById('eventId').value = event.id;
                    document.getElementById('title').value = event.title;
                    document.getElementById('description').value = event.description || '';
                    document.getElementById('topic').value = event.topic;
                    document.getElementById('location').value = event.location;
                    document.getElementById('status').value = event.status;
                    document.getElementById('district_id').value = event.district_id;
                    document.getElementById('scientist_id').value = event.scientist_id;
                    document.getElementById('expected_participants').value = event.expected_participants;
                    document.getElementById('actual_participants').value = event.actual_participants || '';
                    document.getElementById('eventModalLabel').textContent = 'Edit Event';

                    // Format dates for datetime-local input with error handling
                    try {
                        // Handle the date format from backend: "2025-11-06 15:33:00"
                        const startDate = new Date(event.start_date ? event.start_date.replace(' ', 'T') : '');
                        const endDate = new Date(event.end_date ? event.end_date.replace(' ', 'T') : '');

                        if (!isNaN(startDate.getTime())) {
                            document.getElementById('start_date').value = formatDateForInput(startDate);
                        }
                        if (!isNaN(endDate.getTime())) {
                            document.getElementById('end_date').value = formatDateForInput(endDate);
                        }
                    } catch (error) {
                        console.error('Error formatting dates for input:', error);
                    }

                    // Load demographics if available
                    if (event.participant_demographics) {
                        // Gender distribution
                        document.getElementById('male_participants').value = event.participant_demographics.male || '';
                        document.getElementById('female_participants').value = event.participant_demographics.female || '';
                        document.getElementById('transgender_participants').value = event.participant_demographics.transgender || '';

                        // Caste distribution
                        document.getElementById('st_participants').value = event.participant_demographics.st || '';
                        document.getElementById('sc_participants').value = event.participant_demographics.sc || '';
                        document.getElementById('general_participants').value = event.participant_demographics.general || '';
                        document.getElementById('obc_participants').value = event.participant_demographics.obc || '';

                        // Age distribution
                        document.getElementById('age_below_30').value = event.participant_demographics.below_30 || '';
                        document.getElementById('age_30_to_50').value = event.participant_demographics.age_30_to_50 || '';
                        document.getElementById('age_above_50').value = event.participant_demographics.above_50 || '';
                    }

                    toggleDemographicsSection();
                    eventModal.show();
                })
                .catch(error => console.error('Error loading event details:', error));
        }

        // Format date for datetime-local input with validation
        function formatDateForInput(date) {
            if (!date || isNaN(date.getTime())) {
                return '';
            }
            return date.getFullYear() + '-' +
                   padZero(date.getMonth() + 1) + '-' +
                   padZero(date.getDate()) + 'T' +
                   padZero(date.getHours()) + ':' +
                   padZero(date.getMinutes());
        }

        // Pad zero for date formatting
        function padZero(num) {
            return num.toString().padStart(2, '0');
        }

        // Toggle demographics section based on status
        function toggleDemographicsSection() {
            if (statusSelect.value === 'completed') {
                actualParticipantsInput.required = true;
                demographicsSection.style.display = 'block';
            } else {
                actualParticipantsInput.required = false;
                demographicsSection.style.display = 'none';
            }
        }

        // Update participant demographics
        function updateParticipantDemographics() {
            if (statusSelect.value === 'completed') {
                const demographics = {
                    // Gender distribution
                    male: parseInt(document.getElementById('male_participants').value) || 0,
                    female: parseInt(document.getElementById('female_participants').value) || 0,
                    transgender: parseInt(document.getElementById('transgender_participants').value) || 0,

                    // Caste distribution
                    st: parseInt(document.getElementById('st_participants').value) || 0,
                    sc: parseInt(document.getElementById('sc_participants').value) || 0,
                    general: parseInt(document.getElementById('general_participants').value) || 0,
                    obc: parseInt(document.getElementById('obc_participants').value) || 0,

                    // Age distribution
                    below_30: parseInt(document.getElementById('age_below_30').value) || 0,
                    age_30_to_50: parseInt(document.getElementById('age_30_to_50').value) || 0,
                    above_50: parseInt(document.getElementById('age_above_50').value) || 0
                };

                // Calculate total participants from gender distribution
                const genderTotal = demographics.male + demographics.female + demographics.transgender;

                // Calculate total participants from caste distribution
                const casteTotal = demographics.st + demographics.sc + demographics.general + demographics.obc;

                // Update actual participants field if it's empty or zero
                const actualParticipantsField = document.getElementById('actual_participants');
                if (!actualParticipantsField.value || parseInt(actualParticipantsField.value) === 0) {
                    // Use the larger of the two totals
                    actualParticipantsField.value = Math.max(genderTotal, casteTotal);
                }

                participantDemographicsInput.value = JSON.stringify(demographics);
            } else {
                participantDemographicsInput.value = '';
            }
        }

        // Validate form
        function validateForm() {
            if (!eventForm.checkValidity()) {
                eventForm.reportValidity();
                return false;
            }

            // Additional validation
            const startDate = new Date(document.getElementById('start_date').value);
            const endDate = new Date(document.getElementById('end_date').value);

            if (endDate < startDate) {
                alert('End date cannot be before start date.');
                return false;
            }

            if (statusSelect.value === 'completed' && !actualParticipantsInput.value) {
                alert('Actual participants is required for completed events.');
                actualParticipantsInput.focus();
                return false;
            }

            return true;
        }

        // Reset form function
        function resetForm() {
            eventForm.reset();
            document.getElementById('eventId').value = '';
            document.getElementById('participant_demographics').value = '';
        }
    });
</script>
@endpush
