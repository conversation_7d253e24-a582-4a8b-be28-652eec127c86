<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('District  Coordinator Management')); ?></h5>
                    <div>
                        <button id="addCoordinatorBtn" class="btn btn-sm btn-success me-2">Add New Coordinator</button>
                        <a href="<?php echo e(route('super-admin.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Manage District Coordinators who oversee specific districts across states.</li>
                            <li>Each coordinator can be assigned multiple districts from different states.</li>
                            <li>Scientists in those districts will report to their respective district state coordinator.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Designation</th>
                                    <th>Role</th>
                                    <th>Districts</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="coordinatorsTableBody">
                                <!-- Coordinators will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Coordinator Form Modal -->
                    <div class="modal fade" id="coordinatorModal" tabindex="-1" aria-labelledby="coordinatorModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="coordinatorModalLabel">Add New District State Coordinator</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="coordinatorForm">
                                        <input type="hidden" id="coordinatorId" name="id">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="name">Name:</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="email">Email:</label>
                                                    <input type="email" class="form-control" id="email" name="email" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="phone_number">Phone Number:</label>
                                                    <input type="text" class="form-control" id="phone_number" name="phone_number" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="designation">Designation:</label>
                                                    <input type="text" class="form-control" id="designation" name="designation" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="role">Role:</label>
                                                    <select class="form-control" id="role" name="role" required>
                                                        <option value="DSC">District State Coordinator (DSC)</option>
                                                        <option value="Other">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="password">Password:</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="password" name="password" minlength="4">
                                                        <button class="btn btn-outline-secondary" type="button" id="toggleCoordinatorPassword">
                                                            <i class="fas fa-eye" id="coordinatorPasswordIcon"></i>
                                                        </button>
                                                    </div>
                                                    <small class="form-text text-muted" id="passwordHelpText">Leave blank to keep current password when editing.</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group mb-3">
                                                    <label for="expertise_areas">Expertise Areas:</label>
                                                    <div id="expertise_areas_container">
                                                        <div class="input-group mb-2">
                                                            <input type="text" class="form-control expertise-area" placeholder="Enter an area of expertise">
                                                            <button type="button" class="btn btn-success add-expertise-btn">+</button>
                                                        </div>
                                                    </div>
                                                    <div id="expertise_tags" class="mt-2"></div>
                                                    <input type="hidden" id="expertise_areas_json" name="expertise_areas">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3 mt-4">
                                            <label for="district_ids">Assigned Districts:</label>
                                            <div id="districts_container">
                                                <!-- Districts will be grouped by state here -->
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="saveCoordinatorBtn">Save Coordinator</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this district state coordinator? This action cannot be undone.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Coordinator</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const coordinatorsTableBody = document.getElementById('coordinatorsTableBody');
        const addCoordinatorBtn = document.getElementById('addCoordinatorBtn');
        const coordinatorForm = document.getElementById('coordinatorForm');
        const saveCoordinatorBtn = document.getElementById('saveCoordinatorBtn');
        const coordinatorModal = new bootstrap.Modal(document.getElementById('coordinatorModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const expertiseAreasContainer = document.getElementById('expertise_areas_container');
        const expertiseTags = document.getElementById('expertise_tags');
        const expertiseAreasJson = document.getElementById('expertise_areas_json');
        const districtsContainer = document.getElementById('districts_container');
        let currentCoordinatorId = null;
        let expertiseAreas = [];
        let availableDistricts = [];
        let selectedDistricts = [];

        // Password toggle functionality
        const toggleCoordinatorPassword = document.getElementById('toggleCoordinatorPassword');
        if (toggleCoordinatorPassword) {
            toggleCoordinatorPassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const passwordIcon = document.getElementById('coordinatorPasswordIcon');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    passwordIcon.classList.remove('fa-eye');
                    passwordIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    passwordIcon.classList.remove('fa-eye-slash');
                    passwordIcon.classList.add('fa-eye');
                }
            });
        }

        // Load coordinators
        loadCoordinators();

        // Load available districts
        loadAvailableDistricts();

        // Add coordinator button click
        addCoordinatorBtn.addEventListener('click', function() {
            resetForm();
            document.getElementById('coordinatorModalLabel').textContent = 'Add New District State Coordinator';
            document.getElementById('password').required = true;
            document.getElementById('passwordHelpText').textContent = 'Password is required for new coordinators (minimum 4 characters).';
            coordinatorModal.show();
        });

        // Add expertise area button click
        document.addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('add-expertise-btn')) {
                const inputGroup = e.target.closest('.input-group');
                const input = inputGroup.querySelector('.expertise-area');
                const value = input.value.trim();

                if (value) {
                    addExpertiseArea(value);
                    input.value = '';
                }
            }
        });

        // Remove expertise tag click
        document.addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('remove-expertise-btn')) {
                const area = e.target.dataset.area;
                removeExpertiseArea(area);
            }
        });

        // District checkbox change
        document.addEventListener('change', function(e) {
            if (e.target && e.target.classList.contains('district-checkbox')) {
                const districtId = e.target.value;
                if (e.target.checked) {
                    if (!selectedDistricts.includes(districtId)) {
                        selectedDistricts.push(districtId);
                    }
                } else {
                    selectedDistricts = selectedDistricts.filter(id => id !== districtId);
                }
            }
        });

        // Save coordinator button click
        saveCoordinatorBtn.addEventListener('click', function() {
            if (!coordinatorForm.checkValidity()) {
                coordinatorForm.reportValidity();
                return;
            }

            if (selectedDistricts.length === 0) {
                alert('Please select at least one district.');
                return;
            }

            // Update expertise areas JSON
            expertiseAreasJson.value = JSON.stringify(expertiseAreas);

            const formData = new FormData(coordinatorForm);
            const data = {};

            formData.forEach((value, key) => {
                if (key === 'expertise_areas') {
                    data[key] = JSON.parse(value);
                } else if (key === 'designation' || key === 'role') {
                    // Ensure designation and role are properly quoted strings
                    data[key] = String(value);
                } else {
                    data[key] = value;
                }
            });

            // Add selected districts
            data.district_ids = selectedDistricts;

            fetch('<?php echo e(route("super-admin.district-state-coordinator.save")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    coordinatorModal.hide();
                    loadCoordinators();
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving coordinator:', error);
                alert('An error occurred while saving the coordinator information.');
            });
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentCoordinatorId) {
                fetch(`<?php echo e(url("super-admin/district-state-coordinator/delete")); ?>/${currentCoordinatorId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadCoordinators();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting coordinator:', error);
                    alert('An error occurred while deleting the coordinator.');
                });
            }
        });

        // Load coordinators function
        function loadCoordinators() {
            fetch('<?php echo e(route("super-admin.district-state-coordinator.all")); ?>')
                .then(response => response.json())
                .then(data => {
                    coordinatorsTableBody.innerHTML = '';

                    data.forEach(coordinator => {
                        const row = document.createElement('tr');

                        // Create district badges
                        let districtBadges = '';
                        if (coordinator.districts && coordinator.districts.length > 0) {
                            // Group districts by state
                            const stateDistricts = {};
                            coordinator.districts.forEach(district => {
                                if (!stateDistricts[district.state]) {
                                    stateDistricts[district.state] = [];
                                }
                                stateDistricts[district.state].push(district);
                            });

                            // Create badges for each state
                            for (const state in stateDistricts) {
                                districtBadges += `<div class="mb-1"><strong>${state}:</strong> `;
                                stateDistricts[state].forEach(district => {
                                    const badgeClass = district.status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info';
                                    districtBadges += `<span class="badge ${badgeClass} me-1">${district.district}</span>`;
                                });
                                districtBadges += `</div>`;
                            }
                        } else {
                            districtBadges = '<span class="text-muted">No districts assigned</span>';
                        }

                        // Create expertise area badges
                        let expertiseBadges = '';
                        if (coordinator.expertise_areas && coordinator.expertise_areas.length > 0) {
                            coordinator.expertise_areas.forEach(area => {
                                expertiseBadges += `<span class="badge bg-secondary me-1">${area}</span>`;
                            });
                        } else {
                            expertiseBadges = '<span class="text-muted">None specified</span>';
                        }

                        row.innerHTML = `
                            <td>${coordinator.name}</td>
                            <td>${coordinator.email}</td>
                            <td>${coordinator.phone_number || ''}</td>
                            <td>${coordinator.designation || ''}</td>
                            <td><span class="badge bg-primary">${coordinator.role}</span></td>
                            <td>${districtBadges}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="${coordinator.id}">Edit</button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${coordinator.id}">Delete</button>
                            </td>
                        `;

                        coordinatorsTableBody.appendChild(row);
                    });

                    // Add event listeners to edit buttons
                    document.querySelectorAll('.edit-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const coordinatorId = this.dataset.id;
                            loadCoordinatorDetails(coordinatorId);
                        });
                    });

                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            currentCoordinatorId = this.dataset.id;
                            deleteConfirmModal.show();
                        });
                    });
                })
                .catch(error => console.error('Error loading coordinators:', error));
        }

        // Load available districts function
        function loadAvailableDistricts() {
            fetch('<?php echo e(route("super-admin.district-state-coordinator.available-districts")); ?>')
                .then(response => response.json())
                .then(data => {
                    availableDistricts = data;
                    updateDistrictsContainer();
                })
                .catch(error => console.error('Error loading available districts:', error));
        }

        // Update districts container function
        function updateDistrictsContainer() {
            districtsContainer.innerHTML = '';

            // Group districts by state
            const stateDistricts = {};
            availableDistricts.forEach(district => {
                if (!stateDistricts[district.state]) {
                    stateDistricts[district.state] = [];
                }
                stateDistricts[district.state].push(district);
            });

            // Create a card for each state
            for (const state in stateDistricts) {
                const stateCard = document.createElement('div');
                stateCard.className = 'card mb-3';

                const stateHeader = document.createElement('div');
                stateHeader.className = 'card-header';
                stateHeader.innerHTML = `<strong>${state}</strong>`;

                const stateBody = document.createElement('div');
                stateBody.className = 'card-body';

                const districtList = document.createElement('div');
                districtList.className = 'row';

                stateDistricts[state].forEach(district => {
                    const districtCol = document.createElement('div');
                    districtCol.className = 'col-md-4 mb-2';

                    const districtCheck = document.createElement('div');
                    districtCheck.className = 'form-check';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'form-check-input district-checkbox';
                    checkbox.id = `district_${district.id}`;
                    checkbox.value = district.id;
                    checkbox.checked = selectedDistricts.includes(district.id.toString());

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = `district_${district.id}`;
                    label.textContent = `${district.district} (${district.status || 'No status'})`;

                    districtCheck.appendChild(checkbox);
                    districtCheck.appendChild(label);
                    districtCol.appendChild(districtCheck);
                    districtList.appendChild(districtCol);
                });

                stateBody.appendChild(districtList);
                stateCard.appendChild(stateHeader);
                stateCard.appendChild(stateBody);
                districtsContainer.appendChild(stateCard);
            }
        }

        // Load coordinator details function
        function loadCoordinatorDetails(id) {
            fetch(`<?php echo e(url("super-admin/district-state-coordinator/details")); ?>/${id}`)
                .then(response => response.json())
                .then(coordinator => {
                    resetForm();

                    currentCoordinatorId = coordinator.id;
                    document.getElementById('coordinatorId').value = coordinator.id;
                    document.getElementById('name').value = coordinator.name;
                    document.getElementById('email').value = coordinator.email;
                    document.getElementById('phone_number').value = coordinator.phone_number || '';
                    document.getElementById('designation').value = coordinator.designation || '';
                    document.getElementById('role').value = coordinator.role;
                    document.getElementById('coordinatorModalLabel').textContent = 'Edit District State Coordinator';

                    // Handle password field for editing
                    document.getElementById('password').required = false;
                    document.getElementById('password').value = '';
                    document.getElementById('passwordHelpText').textContent = 'Leave blank to keep current password when editing.';

                    // Load expertise areas
                    expertiseAreas = Array.isArray(coordinator.expertise_areas) ? coordinator.expertise_areas : [];
                    updateExpertiseTags();

                    // Select assigned districts
                    if (coordinator.districts && coordinator.districts.length > 0) {
                        selectedDistricts = coordinator.districts.map(district => district.id.toString());
                        updateDistrictsContainer();
                    }

                    coordinatorModal.show();
                })
                .catch(error => console.error('Error loading coordinator details:', error));
        }

        // Add expertise area function
        function addExpertiseArea(area) {
            if (!expertiseAreas.includes(area)) {
                expertiseAreas.push(area);
                updateExpertiseTags();
            }
        }

        // Remove expertise area function
        function removeExpertiseArea(area) {
            expertiseAreas = expertiseAreas.filter(a => a !== area);
            updateExpertiseTags();
        }

        // Update expertise tags function
        function updateExpertiseTags() {
            expertiseTags.innerHTML = '';

            expertiseAreas.forEach(area => {
                const tag = document.createElement('span');
                tag.className = 'badge bg-primary me-2 mb-2';
                tag.innerHTML = `${area} <button type="button" class="btn-close btn-close-white remove-expertise-btn" data-area="${area}" aria-label="Remove"></button>`;
                expertiseTags.appendChild(tag);
            });

            // Update hidden input
            expertiseAreasJson.value = JSON.stringify(expertiseAreas);
        }

        // Reset form function
        function resetForm() {
            coordinatorForm.reset();
            document.getElementById('coordinatorId').value = '';
            document.getElementById('password').value = '';
            document.getElementById('password').required = true;
            document.getElementById('passwordHelpText').textContent = 'Password is required for new coordinators (minimum 4 characters).';
            expertiseAreas = [];
            selectedDistricts = [];
            updateExpertiseTags();
            updateDistrictsContainer();
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/super-admin/district-state-coordinator/index.blade.php ENDPATH**/ ?>