<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActionPlanFeedback extends Model
{
    use HasFactory;

    protected $table = 'actionplan_feedback';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'action_plan_id',
        'event_success_rating',
        'challenges_faced',
        'suggestions_for_improvement',
        'objectives_met',
        'self_assessed_learning_outcome',
        'male_participants',
        'female_participants',
        'transgender_participants',
        'st_participants',
        'sc_participants',
        'general_participants',
        'obc_participants',
        'photo1',
        'photo2',
        'photo3',
        'photo4',
        'gis_location',
        // VIP Participation fields
        'vip_participated',
        'vip_mla_mp',
        'vip_ms_jst_director',
        'vip_local_pr',
        'vip_state_official',
        'vip_others',
        'vip_others_details',
        // Media Coverage fields
        'media_coverage',
        'media_newspaper',
        'media_tv_news',
        'media_radio',
        'media_link',
        'media_photo',
        // Social Media Post Link
        'social_media_link'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'objectives_met' => 'array',
        'vip_participated' => 'boolean',
        'vip_mla_mp' => 'boolean',
        'vip_ms_jst_director' => 'boolean',
        'vip_local_pr' => 'boolean',
        'vip_state_official' => 'boolean',
        'vip_others' => 'boolean',
        'media_coverage' => 'boolean',
        'media_newspaper' => 'boolean',
        'media_tv_news' => 'boolean',
        'media_radio' => 'boolean',
    ];

    /**
     * Get the user who submitted the feedback.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the action plan this feedback belongs to.
     */
    public function actionPlan()
    {
        return $this->belongsTo(ActionPlan::class);
    }
}
