<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Http\Controllers\FormBuilderController;
use App\Http\Controllers\InputTypeController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\PasswordChangeController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\DistrictUserController;
use App\Http\Controllers\SuperAdmin\DashboardController as SuperAdminDashboardController;
use App\Http\Controllers\Scientist\DashboardController as ScientistDashboardController;
use App\Http\Controllers\Scientist\FormController as ScientistFormController;
use App\Http\Controllers\Scientist\SwotAnalysisController;
use App\Http\Controllers\Scientist\ActionPlanController;

use App\Http\Controllers\Scientist\CreateTestActionPlanController;
use App\Http\Controllers\Scientist\SyncCompletedActionPlansController;
use App\Http\Controllers\Scientist\ParticipationRequestController;
use App\Http\Controllers\User\DashboardController as UserDashboardController;
use App\Http\Controllers\UserFeedbackController;
use App\Http\Controllers\ActionPlanFeedbackController;
use App\Http\Controllers\ZonalCoordinator\ScientistController;

// Home route with redirection based on authentication status and user role
// Note: The actual home route for unauthenticated users is now handled in web_public.php
Route::get('/dashboard', function () {
    if (Auth::check()) {
        $user = Auth::user();
        $roleRoutes = [
            'super_admin' => 'super-admin.dashboard',
            'admin' => 'admin.dashboard',
            'scientist' => 'scientist.dashboard',
            'zonal_coordinator' => 'zonal-coordinator.dashboard',
            'district_state_coordinator' => 'district-state-coordinator.dashboard',
            'user' => 'user.dashboard',
        ];

        return redirect()->route($roleRoutes[$user->role] ?? 'user.dashboard');
    }

    return redirect()->route('login');
})->name('dashboard.redirect');

// Test route (restricted to local environment)
Route::get('/test', function () {
    return 'Test route is working!';
})->middleware('env:local');

// Debug SWOT route (restricted to local environment)
Route::get('/debug-swot', function () {
    try {
        $tableExists = Schema::hasTable('swot');

        if ($tableExists) {
            $swot = new \App\Models\Swot();
            $swot->scientist_id = Auth::id() ?? 1; // Use authenticated user ID or fallback
            $swot->district_id = 1; // Ensure this ID exists or validate dynamically
            $swot->strengths = 'Test strengths';
            $swot->weaknesses = 'Test weaknesses';
            $swot->opportunities = 'Test opportunities';
            $swot->threats = 'Test threats';
            $swot->save();

            return response()->json([
                'table_exists' => $tableExists,
                'swot_created' => true,
                'swot_id' => $swot->id,
            ]);
        }

        return response()->json([
            'table_exists' => $tableExists,
            'error' => 'Table does not exist',
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 500);
    }
})->middleware('env:local');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login'])->middleware('auth.ratelimit');
    Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('register', [RegisterController::class, 'register'])->middleware('auth.ratelimit');
});

Route::post('logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Password Change Routes
Route::middleware('auth')->group(function () {
    Route::get('change-password', [PasswordChangeController::class, 'showChangeForm'])->name('password.change');
    Route::post('change-password', [PasswordChangeController::class, 'changePassword'])->name('password.update');
});

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');

    // District User Management Routes
    Route::get('district-users', [DistrictUserController::class, 'index'])->name('admin.district-users');
    Route::get('get-states', [DistrictUserController::class, 'getStates'])->name('admin.get-states');
    Route::get('get-districts/{state}', [DistrictUserController::class, 'getDistrictsByState'])->name('admin.get-districts');
    Route::get('get-district-details/{id}', [DistrictUserController::class, 'getDistrictDetails'])->name('admin.get-district-details');
    Route::post('save-district-user', [DistrictUserController::class, 'saveDistrictUser'])->name('admin.save-district-user');

    // Form Builder Routes
    Route::get('form-builder', [FormBuilderController::class, 'index'])->name('admin.form-builder');
    Route::get('form-builder/create', [FormBuilderController::class, 'create'])->name('admin.form-builder.create');
    Route::post('form-builder', [FormBuilderController::class, 'store'])->name('admin.form-builder.store');
    Route::delete('form-builder/{id}', [FormBuilderController::class, 'destroy'])->name('admin.form-builder.destroy');
    Route::get('form-builder/{id}/edit', [FormBuilderController::class, 'edit'])->name('admin.form-builder.edit');
    Route::post('form-builder/{id}', [FormBuilderController::class, 'update'])->name('admin.form-builder.update');
});

// Scientist Routes
Route::middleware(['auth', 'role:scientist'])->prefix('scientist')->group(function () {
    Route::get('dashboard', [ScientistDashboardController::class, 'index'])->name('scientist.dashboard');
    Route::get('get-stats', [ScientistDashboardController::class, 'getStats'])->name('scientist.get-stats');

    // Form Routes
    Route::get('forms', [ScientistFormController::class, 'index'])->name('scientist.forms');
    Route::get('forms/data', [ScientistFormController::class, 'getForms'])->name('scientist.get-forms');
    Route::get('forms/{id}', [ScientistFormController::class, 'getFormDetails'])->name('scientist.get-form');
    Route::get('forms/{id}/read', [ScientistFormController::class, 'read'])->name('scientist.read-form');
    Route::post('forms/submit', [InputTypeController::class, 'submitForm'])->middleware('secure.upload')->name('scientist.submit-form');

    // SWOT Analysis Routes
    Route::get('swot', [SwotAnalysisController::class, 'handleSwotRequest'])->name('scientist.swot');
    Route::get('swot/data', [SwotAnalysisController::class, 'getSwotAnalysis'])->name('scientist.swot.get');
    Route::post('swot/save', [SwotAnalysisController::class, 'saveSwotAnalysis'])->name('scientist.swot.save');

    // Action Plan Routes
    Route::get('action-plans', [ActionPlanController::class, 'index'])->name('scientist.action-plans');
    Route::get('action-plans/data', [ActionPlanController::class, 'getActionPlans'])->name('scientist.action-plans.get');
    Route::get('action-plans/{id}', [ActionPlanController::class, 'getActionPlan'])->name('scientist.action-plans.get-one');
    Route::get('action-plans/coordinators', [ActionPlanController::class, 'getCoordinators'])->name('scientist.action-plans.coordinators');
    Route::post('action-plans', [ActionPlanController::class, 'createActionPlan'])->name('scientist.action-plans.create');
    Route::post('action-plans/{id}', [ActionPlanController::class, 'updateActionPlan'])->name('scientist.action-plans.update');
    Route::post('action-plans/{id}/cancel', [ActionPlanController::class, 'cancelActionPlan'])->name('scientist.action-plans.cancel');
    Route::post('action-plans/{id}/report', [ActionPlanController::class, 'submitReport'])->name('scientist.action-plans.report');

    // DSC Invitation Routes
    Route::get('dsc-invitation/{actionPlan}', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'index'])->name('scientist.dsc-invitation');
    Route::get('dsc-invitation/available/dscs', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'getAvailableDSCs'])->name('scientist.dsc-invitation.available');
    Route::post('dsc-invitation/send', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'sendInvitations'])->name('scientist.dsc-invitation.send');
    Route::get('dsc-invitation/{actionPlan}/invited', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'getInvitedDSCs'])->name('scientist.dsc-invitation.invited');

    // Participant Review System Routes
    Route::prefix('feedback-import')->name('scientist.feedback-import.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'index'])->name('index');
        Route::get('/get-events', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'getEvents'])->name('get-events');
        Route::get('/event-feedback/{eventId}', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'getEventFeedback'])->name('get-event-feedback');
        Route::get('/download-template', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'downloadTemplate'])->name('download-template');
        Route::post('/import', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'importFeedback'])->name('import');
        Route::get('/generate-qr/{eventId}', [\App\Http\Controllers\Scientist\FeedbackImportController::class, 'generateQrCode'])->name('generate-qr');
    });

    // Legacy feedback route redirect
    Route::get('feedback', function() {
        return redirect()->route('scientist.feedback-import.index');
    })->name('scientist.feedback');

    // Participation Request Routes
    Route::get('participation-requests', [ParticipationRequestController::class, 'index'])->name('scientist.participation-requests');
    Route::get('participation-requests/data', [ParticipationRequestController::class, 'getRequests'])->name('scientist.participation-requests.get');
    Route::get('participation-requests/coordinators', [ParticipationRequestController::class, 'getAvailableCoordinators'])->name('scientist.participation-requests.coordinators');
    Route::post('participation-requests', [ParticipationRequestController::class, 'createRequest'])->name('scientist.participation-requests.create');

    // Test and Sync Routes
    Route::get('create-test-action-plan', [CreateTestActionPlanController::class, 'createTestActionPlan'])->name('scientist.create-test-action-plan');
    Route::get('sync-completed-action-plans', [SyncCompletedActionPlansController::class, 'syncCompletedActionPlans'])->name('scientist.sync-completed-action-plans');



    // Debug Routes (restricted to local environment)
    Route::get('debug-action-plans', function () {
        $scientistId = Auth::id();
        $allPlans = \App\Models\ActionPlan::where('scientist_id', $scientistId)->get();
        $completedPlans = \App\Models\ActionPlan::where('scientist_id', $scientistId)->where('status', 'completed')->get();

        return response()->json([
            'scientist_id' => $scientistId,
            'all_plans_count' => $allPlans->count(),
            'all_plans' => $allPlans->map(fn($plan) => [
                'id' => $plan->id,
                'title' => $plan->title,
                'type' => $plan->type,
                'status' => $plan->status,
                'planned_date' => $plan->planned_date,
                'created_at' => $plan->created_at,
            ]),
            'completed_plans_count' => $completedPlans->count(),
            'completed_plans' => $completedPlans->map(fn($plan) => [
                'id' => $plan->id,
                'title' => $plan->title,
                'type' => $plan->type,
                'status' => $plan->status,
                'planned_date' => $plan->planned_date,
                'created_at' => $plan->created_at,
            ]),
        ]);
    })->middleware('env:local');

    Route::get('debug-events', function () {
        $scientistId = Auth::id();
        $allEvents = \App\Models\Event::where('scientist_id', $scientistId)->get();
        $completedEvents = \App\Models\Event::where('scientist_id', $scientistId)->where('status', 'completed')->get();

        return response()->json([
            'scientist_id' => $scientistId,
            'all_events_count' => $allEvents->count(),
            'all_events' => $allEvents->map(fn($event) => [
                'id' => $event->id,
                'title' => $event->title,
                'status' => $event->status,
                'start_date' => $event->start_date,
                'created_at' => $event->created_at,
            ]),
            'completed_events_count' => $completedEvents->count(),
            'completed_events' => $completedEvents->map(fn($event) => [
                'id' => $event->id,
                'title' => $event->title,
                'status' => $event->status,
                'start_date' => $event->start_date,
                'created_at' => $event->created_at,
            ]),
        ]);
    })->middleware('env:local');
});

// User Routes
Route::middleware(['auth', 'role:user'])->prefix('user')->group(function () {
    Route::get('dashboard', [UserDashboardController::class, 'index'])->name('user.dashboard');
});

// Super Admin Routes
Route::middleware(['auth', 'role:super_admin'])->prefix('super-admin')->group(function () {
    Route::get('dashboard', [SuperAdminDashboardController::class, 'index'])->name('super-admin.dashboard');
});

// Zonal Coordinator Routes
Route::middleware(['auth', 'role:zonal_coordinator'])->prefix('zonal-coordinator')->group(function () {
    Route::get('scientists', [ScientistController::class, 'index'])->name('scientists.index');
    Route::get('scientists/{id}', [ScientistController::class, 'show'])->name('scientists.show');
});

// Feedback Routes
Route::middleware('auth')->prefix('feedback')->group(function () {
    Route::get('{actionPlanId}', [UserFeedbackController::class, 'showFeedbackForm'])->name('feedback.form');
    Route::post('submit', [UserFeedbackController::class, 'submitFeedback'])->name('feedback.submit');
    Route::get('template/download', [UserFeedbackController::class, 'downloadTemplate'])->name('feedback.template.download');
    Route::post('import', [UserFeedbackController::class, 'importFeedback'])->name('feedback.import');
    Route::get('list/{actionPlanId}', [UserFeedbackController::class, 'getFeedbackList'])->name('feedback.list');
    Route::get('{actionPlanId}/view', [ActionPlanFeedbackController::class, 'viewFeedback'])->name('feedback.view');
    Route::get('{actionPlanId}/export', [ActionPlanFeedbackController::class, 'exportFeedback'])->name('feedback.export');
    Route::post('{actionPlanId}/import', [ActionPlanFeedbackController::class, 'importFeedback'])->name('feedback.import');
});

// Debug routes (only in local environment)
Route::middleware('env:local')->prefix('debug')->group(function () {
    Route::get('form/{formId}', [App\Http\Controllers\FormBuilderDebugController::class, 'debugForm'])->name('debug.form');
    Route::get('forms/status', [App\Http\Controllers\FormBuilderDebugController::class, 'listFormsStatus'])->name('debug.forms.status');
    Route::post('forms/test', [App\Http\Controllers\FormBuilderDebugController::class, 'testFormCreation'])->name('debug.forms.test');
    Route::post('forms/fix', [App\Http\Controllers\FormBuilderDebugController::class, 'fixCorruptedForms'])->name('debug.forms.fix');
});

// Include additional route files
require __DIR__ . '/web_super_admin.php';
require __DIR__ . '/web_zonal_coordinator.php';
require __DIR__ . '/web_district_state_coordinator.php';
require __DIR__ . '/web_public.php';
