<?php $__env->startSection('title', 'Profile Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Profile Management</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo e($error); ?>

                        </div>
                    <?php elseif(isset($welcomeMessage)): ?>
                        <div class="alert alert-info">
                            <?php echo e($welcomeMessage); ?>

                        </div>
                    <?php else: ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title">Personal Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Name:</label>
                                            <p><?php echo e($coordinator->name); ?></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Email:</label>
                                            <p><?php echo e($coordinator->email); ?></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Phone Number:</label>
                                            <p><?php echo e($coordinator->phone_number); ?></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Designation:</label>
                                            <p><?php echo e($coordinator->designation); ?></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Role:</label>
                                            <p><?php echo e($coordinator->role); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title">Areas of Expertise</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="expertiseForm">
                                            <div id="expertise_areas_container">
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control expertise-area" placeholder="Enter an area of expertise">
                                                    <button type="button" class="btn btn-success add-expertise-btn">+</button>
                                                </div>
                                            </div>
                                            <div id="expertise_tags" class="mt-2">
                                                <?php if(isset($coordinator->expertise_areas) && is_array($coordinator->expertise_areas)): ?>
                                                    <?php $__currentLoopData = $coordinator->expertise_areas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $area): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span class="badge bg-primary me-2 mb-2 p-2 expertise-tag">
                                                            <?php echo e($area); ?>

                                                            <i class="fas fa-times ms-1 remove-expertise" data-area="<?php echo e($area); ?>"></i>
                                                        </span>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </div>
                                            <input type="hidden" id="expertise_areas_json" name="expertise_areas">
                                            <button type="button" id="saveExpertiseBtn" class="btn btn-primary mt-3">Save Expertise Areas</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Your Assigned Districts</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php if(count($districts) > 0): ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>State</th>
                                                            <th>District</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $districts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $district): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td><?php echo e($district->state); ?></td>
                                                                <td><?php echo e($district->district); ?></td>
                                                                <td>
                                                                    <span class="badge <?php echo e($district->status == 'Pre-Cocoon' ? 'bg-info' : 'bg-warning'); ?>">
                                                                        <?php echo e($district->status); ?>

                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-info">
                                                No districts have been assigned to you yet.
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const expertiseAreasContainer = document.getElementById('expertise_areas_container');
        const expertiseTags = document.getElementById('expertise_tags');
        const expertiseAreasJson = document.getElementById('expertise_areas_json');
        const saveExpertiseBtn = document.getElementById('saveExpertiseBtn');
        let expertiseAreas = [];

        // Initialize expertise areas from existing tags
        document.querySelectorAll('.expertise-tag').forEach(tag => {
            expertiseAreas.push(tag.textContent.trim());
        });

        // Update the hidden input with the current expertise areas
        function updateExpertiseAreasJson() {
            expertiseAreasJson.value = JSON.stringify(expertiseAreas);
        }

        // Add expertise area
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('add-expertise-btn')) {
                const input = e.target.parentElement.querySelector('.expertise-area');
                const area = input.value.trim();

                if (area && !expertiseAreas.includes(area)) {
                    expertiseAreas.push(area);

                    const tag = document.createElement('span');
                    tag.className = 'badge bg-primary me-2 mb-2 p-2 expertise-tag';
                    tag.innerHTML = `${area} <i class="fas fa-times ms-1 remove-expertise" data-area="${area}"></i>`;
                    expertiseTags.appendChild(tag);

                    input.value = '';
                    updateExpertiseAreasJson();
                }
            }
        });

        // Remove expertise area
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-expertise')) {
                const area = e.target.getAttribute('data-area');
                expertiseAreas = expertiseAreas.filter(a => a !== area);

                e.target.parentElement.remove();
                updateExpertiseAreasJson();
            }
        });

        // Save expertise areas
        saveExpertiseBtn.addEventListener('click', function() {
            updateExpertiseAreasJson();

            fetch('<?php echo e(route("district-state-coordinator.profile.update-expertise")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({
                    expertise_areas: expertiseAreas
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                } else if (data.error) {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving expertise areas');
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/district-state-coordinator/profile.blade.php ENDPATH**/ ?>