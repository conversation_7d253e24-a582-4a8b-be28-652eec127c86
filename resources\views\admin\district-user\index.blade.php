@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">District User Management</h5>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="stateSelect" class="form-label">Select State</label>
                                <select id="stateSelect" class="form-select">
                                    <option value="">-- Select State --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="districtSelect" class="form-label">Select District</label>
                                <select id="districtSelect" class="form-select" disabled>
                                    <option value="">-- Select District --</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="districtUserForm" class="mt-4" style="display: none;">
                        <h4 class="mb-3">District Details</h4>
                        <form id="saveDistrictUserForm">
                            <input type="hidden" id="districtId" name="district_id">
                            <input type="hidden" id="stateName" name="state_name">
                            <input type="hidden" id="userId" name="user_id">
                            <input type="hidden" id="isNew" name="is_new" value="true">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="districtName" class="form-label">District Name</label>
                                        <input type="text" id="districtName" name="district_name" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="districtStatus" class="form-label">District Type</label>
                                        <select id="districtStatus" name="status" class="form-select" required>
                                            <option value="">-- Select Type --</option>
                                            <option value="Pre-Cocoon">Pre-Cocoon</option>
                                            <option value="Post-Cocoon">Post-Cocoon</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <h4 class="mb-3 mt-4">Scientist Details</h4>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="scientistName" class="form-label">Scientist Name</label>
                                        <input type="text" id="scientistName" name="name" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="scientistEmail" class="form-label">Scientist Email</label>
                                        <input type="email" id="scientistEmail" name="email" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="scientistPhone" class="form-label">Scientist Phone</label>
                                        <input type="text" id="scientistPhone" name="phone_number" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="scientistPassword" class="form-label">Password</label>
                                        <div class="input-group">
                                            <input type="password" id="scientistPassword" name="password" class="form-control">
                                            <button class="btn btn-outline-secondary" type="button" id="toggleAdminPassword">
                                                <i class="fas fa-eye" id="adminPasswordIcon"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted" id="passwordHelpText"></small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <button type="submit" class="btn btn-primary">Save District User</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/district-user-manager.js') }}"></script>
@endpush
