<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ScientistManagementController extends Controller
{
    /**
     * Display the scientist management page.
     */
    public function index()
    {
        return view('super-admin.scientist-management.index');
    }

    /**
     * Get all scientists.
     */
    public function getScientists()
    {
        try {
            $scientists = User::where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->get();

            // Get district assignments for each scientist
            foreach ($scientists as $scientist) {
                $districts = StateData::where('scientist', $scientist->email)
                    ->select('id', 'state', 'district', 'status')
                    ->get();

                $scientist->districts = $districts;
            }

            return response()->json($scientists);
        } catch (\Exception $e) {
            Log::error('Error getting scientists: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientists'], 500);
        }
    }

    /**
     * Get scientist details.
     */
    public function getScientistDetails($id)
    {
        try {
            $scientist = User::where('id', $id)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            // Get district assignments
            $districts = StateData::where('scientist', $scientist->email)
                ->select('id', 'state', 'district', 'status')
                ->get();

            $scientist->districts = $districts;

            return response()->json($scientist);
        } catch (\Exception $e) {
            Log::error('Error getting scientist details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientist details'], 500);
        }
    }

    /**
     * Create or update a scientist.
     */
    public function saveScientist(Request $request)
    {
        Log::info('SaveScientist called with data:', $request->all());

        // Custom validation for password based on is_new field
        $rules = [
            'id' => 'nullable|integer|exists:users,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone_number' => 'required|string|max:20',
            'designation' => 'required|string|max:100',
        ];

        // Only require password for new users
        if ($request->input('is_new') === 'true' || $request->input('is_new') === true) {
            $rules['password'] = 'required|string|min:4';
        } else {
            $rules['password'] = 'nullable|string|min:4';
        }



        $request->validate($rules);

        try {
            $user = null;

            // If ID is provided, find user by ID (for updates)
            if ($request->filled('id')) {
                $user = User::find($request->id);

                if (!$user) {
                    return response()->json(['error' => 'User not found'], 404);
                }

                // Check if email is being changed to an existing email
                if ($user && $user->email !== $request->email) {
                    $existingEmailUser = User::where('email', $request->email)
                        ->where('id', '!=', $user->id)
                        ->first();

                    if ($existingEmailUser) {
                        return response()->json(['error' => 'Email already exists for another user'], 422);
                    }
                }
            } else {
                // For new users, check if email already exists
                $existingEmailUser = User::where('email', $request->email)->first();
                if ($existingEmailUser) {
                    return response()->json(['error' => 'Email already exists'], 422);
                }
            }

            if ($user) {
                // Store old email to update district assignments if email changes
                $oldEmail = $user->email;

                // Update existing user
                $user->name = $request->name;
                $user->email = $request->email;
                $user->phone_number = $request->phone_number;
                $user->designation = $request->designation;

                // Update password if provided
                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                }

                $user->save();

                // If email changed, update district assignments in state_data table
                if ($oldEmail !== $request->email) {
                    \DB::table('state_data')
                        ->where('scientist', $oldEmail)
                        ->update(['scientist' => $request->email]);

                    Log::info('Updated district assignments for scientist email change', [
                        'old_email' => $oldEmail,
                        'new_email' => $request->email
                    ]);
                }

                return response()->json(['message' => 'Scientist updated successfully', 'id' => $user->id]);
            } else {
                // Create new scientist user
                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'designation' => $request->designation,
                    'password' => Hash::make($request->password),
                    'role' => 'scientist',
                ]);

                return response()->json(['message' => 'Scientist created successfully', 'id' => $user->id]);
            }
        } catch (\Exception $e) {
            Log::error('Error saving scientist: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save scientist'], 500);
        }
    }

    /**
     * Delete a scientist.
     */
    public function deleteScientist($id)
    {
        try {
            Log::info('Attempting to delete scientist', ['id' => $id]);

            $scientist = User::where('id', $id)
                ->where('role', 'scientist')
                ->first();

            if (!$scientist) {
                Log::warning('Scientist not found', ['id' => $id]);
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            Log::info('Found scientist to delete', [
                'id' => $scientist->id,
                'email' => $scientist->email
            ]);

            // Check for related data that might prevent deletion
            $actionPlansCount = \App\Models\ActionPlan::where('scientist_id', $scientist->id)->count();
            $swotAnalysesCount = \App\Models\SwotAnalysis::where('scientist_id', $scientist->id)->count();

            Log::info('Related data check', [
                'action_plans_count' => $actionPlansCount,
                'swot_analyses_count' => $swotAnalysesCount
            ]);

            // Remove scientist from any district assignments
            $updatedDistricts = StateData::where('scientist', $scientist->email)
                ->update(['scientist' => null]);

            Log::info('Updated district assignments', [
                'scientist_email' => $scientist->email,
                'districts_updated' => $updatedDistricts
            ]);

            // Delete the scientist (related data should cascade delete due to foreign key constraints)
            $scientist->delete();

            Log::info('Successfully deleted scientist', ['id' => $id]);

            return response()->json(['message' => 'Scientist deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting scientist', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to delete scientist: ' . $e->getMessage()], 500);
        }
    }
}
