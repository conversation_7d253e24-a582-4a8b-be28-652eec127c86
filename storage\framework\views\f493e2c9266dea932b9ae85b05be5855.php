<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Events in My Zone')); ?></h5>
                    <a href="<?php echo e(route('zonal-coordinator.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>View all events conducted in districts within your zone.</li>
                            <li>Click on an event to view its details.</li>
                            <li>Use the filters to narrow down the events list.</li>
                        </ul>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="statusFilter">Filter by Status:</label>
                                <select class="form-control" id="statusFilter">
                                    <option value="">All Statuses</option>
                                    <option value="planned">Planned</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="districtFilter">Filter by District:</label>
                                <select class="form-control" id="districtFilter">
                                    <option value="">All Districts</option>
                                    <!-- Districts will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="scientistFilter">Filter by Scientist:</label>
                                <select class="form-control" id="scientistFilter">
                                    <option value="">All Scientists</option>
                                    <!-- Scientists will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="dateFilter">Filter by Date:</label>
                                <select class="form-control" id="dateFilter">
                                    <option value="">All Dates</option>
                                    <option value="upcoming">Upcoming Events</option>
                                    <option value="past">Past Events</option>
                                    <option value="today">Today's Events</option>
                                    <option value="this-week">This Week</option>
                                    <option value="this-month">This Month</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>District</th>
                                    <th>Scientist</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- Events will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Event Details Modal -->
                    <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Event Information</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Title</th>
                                                    <td id="eventTitle"></td>
                                                </tr>
                                                <tr>
                                                    <th>Topic</th>
                                                    <td id="eventTopic"></td>
                                                </tr>
                                                <tr>
                                                    <th>Description</th>
                                                    <td id="eventDescription"></td>
                                                </tr>
                                                <tr>
                                                    <th>Start Date</th>
                                                    <td id="eventStartDate"></td>
                                                </tr>
                                                <tr>
                                                    <th>End Date</th>
                                                    <td id="eventEndDate"></td>
                                                </tr>
                                                <tr>
                                                    <th>Location</th>
                                                    <td id="eventLocation"></td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td id="eventStatus"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>District & Scientist</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>District</th>
                                                    <td id="eventDistrict"></td>
                                                </tr>
                                                <tr>
                                                    <th>State</th>
                                                    <td id="eventState"></td>
                                                </tr>
                                                <tr>
                                                    <th>Scientist</th>
                                                    <td id="eventScientist"></td>
                                                </tr>
                                                <tr>
                                                    <th>Scientist Email</th>
                                                    <td id="eventScientistEmail"></td>
                                                </tr>
                                            </table>

                                            <h6 class="mt-3">Participation</h6>
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>Expected Participants</th>
                                                    <td id="eventExpectedParticipants"></td>
                                                </tr>
                                                <tr>
                                                    <th>Actual Participants</th>
                                                    <td id="eventActualParticipants"></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <div id="demographicsSection" class="mt-4" style="display: none;">
                                        <h6>Participant Demographics</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <table class="table table-bordered">
                                                    <tr>
                                                        <th>Male Participants</th>
                                                        <td id="malePart"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Female Participants</th>
                                                        <td id="femalePart"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <table class="table table-bordered">
                                                    <tr>
                                                        <th>Below 30 Years</th>
                                                        <td id="below30"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>30-50 Years</th>
                                                        <td id="age30to50"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Above 50 Years</th>
                                                        <td id="above50"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const eventsTableBody = document.getElementById('eventsTableBody');
        const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
        const statusFilter = document.getElementById('statusFilter');
        const districtFilter = document.getElementById('districtFilter');
        const scientistFilter = document.getElementById('scientistFilter');
        const dateFilter = document.getElementById('dateFilter');
        let allEvents = [];
        let districts = [];
        let scientists = [];

        // Load events
        loadEvents();

        // Load districts for filter
        loadDistricts();

        // Load scientists for filter
        loadScientists();

        // Filter change events
        statusFilter.addEventListener('change', filterEvents);
        districtFilter.addEventListener('change', filterEvents);
        scientistFilter.addEventListener('change', filterEvents);
        dateFilter.addEventListener('change', filterEvents);

        // Load events function
        function loadEvents() {
            fetch('<?php echo e(route("zonal-coordinator.events.get-events")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }
                    
                    allEvents = data;
                    displayEvents(allEvents);
                })
                .catch(error => {
                    console.error('Error loading events:', error);
                    alert('An error occurred while loading events.');
                });
        }

        // Load districts function
        function loadDistricts() {
            fetch('<?php echo e(route("zonal-coordinator.districts.get-districts")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading districts:', data.error);
                        return;
                    }
                    
                    districts = data;
                    
                    // Populate district filter
                    data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = `${district.district} (${district.state})`;
                        districtFilter.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading districts:', error);
                });
        }

        // Load scientists function
        function loadScientists() {
            fetch('<?php echo e(route("zonal-coordinator.scientists.get-scientists")); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading scientists:', data.error);
                        return;
                    }
                    
                    scientists = data;
                    
                    // Populate scientist filter
                    data.forEach(scientist => {
                        const option = document.createElement('option');
                        option.value = scientist.id;
                        option.textContent = scientist.name;
                        scientistFilter.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading scientists:', error);
                });
        }

        // Display events function
        function displayEvents(events) {
            eventsTableBody.innerHTML = '';
            
            events.forEach(event => {
                const row = document.createElement('tr');
                
                // Format date with error handling
                let formattedDate = 'No Date';
                if (event.start_date) {
                    try {
                        // Handle the date format from backend: "2025-11-06 15:33:00"
                        const startDate = new Date(event.start_date.replace(' ', 'T'));
                        if (!isNaN(startDate.getTime())) {
                            formattedDate = startDate.toLocaleDateString('en-IN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit'
                            }) + ' ' + startDate.toLocaleTimeString('en-IN', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            });
                        } else {
                            formattedDate = 'Invalid Date';
                        }
                    } catch (error) {
                        console.error('Date parsing error:', error);
                        formattedDate = 'Date Error';
                    }
                }
                
                // Format status
                let statusBadge = '';
                if (event.status === 'planned') {
                    statusBadge = '<span class="badge bg-primary">Planned</span>';
                } else if (event.status === 'completed') {
                    statusBadge = '<span class="badge bg-success">Completed</span>';
                } else if (event.status === 'cancelled') {
                    statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                }
                
                // Format participants
                let participants = `${event.actual_participants || 0} / ${event.expected_participants}`;
                
                row.innerHTML = `
                    <td>${event.title}</td>
                    <td>${formattedDate}</td>
                    <td>${event.district ? event.district.district : ''}</td>
                    <td>${event.scientist ? event.scientist.name : ''}</td>
                    <td>${participants}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-primary view-btn" data-id="${event.id}">View Details</button>
                    </td>
                `;
                
                eventsTableBody.appendChild(row);
            });
            
            // Add event listeners to view buttons
            document.querySelectorAll('.view-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const eventId = this.dataset.id;
                    loadEventDetails(eventId);
                });
            });
        }

        // Filter events function
        function filterEvents() {
            const status = statusFilter.value;
            const districtId = districtFilter.value;
            const scientistId = scientistFilter.value;
            const dateRange = dateFilter.value;
            
            let filteredEvents = allEvents;
            
            // Filter by status
            if (status) {
                filteredEvents = filteredEvents.filter(event => event.status === status);
            }
            
            // Filter by district
            if (districtId) {
                filteredEvents = filteredEvents.filter(event => event.district_id == districtId);
            }
            
            // Filter by scientist
            if (scientistId) {
                filteredEvents = filteredEvents.filter(event => event.scientist_id == scientistId);
            }
            
            // Filter by date
            if (dateRange) {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                
                const weekStart = new Date(today);
                weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekEnd.getDate() + 7);
                
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                
                switch (dateRange) {
                    case 'upcoming':
                        filteredEvents = filteredEvents.filter(event => new Date(event.start_date) >= today);
                        break;
                    case 'past':
                        filteredEvents = filteredEvents.filter(event => new Date(event.start_date) < today);
                        break;
                    case 'today':
                        filteredEvents = filteredEvents.filter(event => {
                            const eventDate = new Date(event.start_date);
                            return eventDate >= today && eventDate < tomorrow;
                        });
                        break;
                    case 'this-week':
                        filteredEvents = filteredEvents.filter(event => {
                            const eventDate = new Date(event.start_date);
                            return eventDate >= weekStart && eventDate < weekEnd;
                        });
                        break;
                    case 'this-month':
                        filteredEvents = filteredEvents.filter(event => {
                            const eventDate = new Date(event.start_date);
                            return eventDate >= monthStart && eventDate <= monthEnd;
                        });
                        break;
                }
            }
            
            displayEvents(filteredEvents);
        }

        // Load event details function
        function loadEventDetails(id) {
            fetch(`<?php echo e(url("zonal-coordinator/events/event")); ?>/${id}`)
                .then(response => response.json())
                .then(event => {
                    if (event.error) {
                        alert(event.error);
                        return;
                    }
                    
                    // Format dates with error handling
                    let formattedStartDate = 'No Date';
                    let formattedEndDate = 'No Date';

                    if (event.start_date) {
                        try {
                            // Handle the date format from backend: "2025-11-06 15:33:00"
                            const startDate = new Date(event.start_date.replace(' ', 'T'));
                            if (!isNaN(startDate.getTime())) {
                                formattedStartDate = startDate.toLocaleDateString('en-IN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                }) + ' ' + startDate.toLocaleTimeString('en-IN', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });
                            } else {
                                formattedStartDate = 'Invalid Date';
                            }
                        } catch (error) {
                            formattedStartDate = 'Date Error';
                        }
                    }

                    if (event.end_date) {
                        try {
                            // Handle the date format from backend: "2025-11-06 15:33:00"
                            const endDate = new Date(event.end_date.replace(' ', 'T'));
                            if (!isNaN(endDate.getTime())) {
                                formattedEndDate = endDate.toLocaleDateString('en-IN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                }) + ' ' + endDate.toLocaleTimeString('en-IN', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });
                            } else {
                                formattedEndDate = 'Invalid Date';
                            }
                        } catch (error) {
                            formattedEndDate = 'Date Error';
                        }
                    }
                    
                    // Format status
                    let statusBadge = '';
                    if (event.status === 'planned') {
                        statusBadge = '<span class="badge bg-primary">Planned</span>';
                    } else if (event.status === 'completed') {
                        statusBadge = '<span class="badge bg-success">Completed</span>';
                    } else if (event.status === 'cancelled') {
                        statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                    }
                    
                    // Update event information
                    document.getElementById('eventTitle').textContent = event.title;
                    document.getElementById('eventTopic').textContent = event.topic;
                    document.getElementById('eventDescription').textContent = event.description || 'No description provided';
                    document.getElementById('eventStartDate').textContent = formattedStartDate;
                    document.getElementById('eventEndDate').textContent = formattedEndDate;
                    document.getElementById('eventLocation').textContent = event.location;
                    document.getElementById('eventStatus').innerHTML = statusBadge;
                    document.getElementById('eventDistrict').textContent = event.district ? event.district.district : '';
                    document.getElementById('eventState').textContent = event.district ? event.district.state : '';
                    document.getElementById('eventScientist').textContent = event.scientist ? event.scientist.name : '';
                    document.getElementById('eventScientistEmail').textContent = event.scientist ? event.scientist.email : '';
                    document.getElementById('eventExpectedParticipants').textContent = event.expected_participants;
                    document.getElementById('eventActualParticipants').textContent = event.actual_participants || 'Not recorded yet';
                    
                    // Show demographics if available
                    const demographicsSection = document.getElementById('demographicsSection');
                    
                    if (event.participant_demographics && event.status === 'completed') {
                        document.getElementById('malePart').textContent = event.participant_demographics.male || '0';
                        document.getElementById('femalePart').textContent = event.participant_demographics.female || '0';
                        document.getElementById('below30').textContent = event.participant_demographics.below_30 || '0';
                        document.getElementById('age30to50').textContent = event.participant_demographics.age_30_to_50 || '0';
                        document.getElementById('above50').textContent = event.participant_demographics.above_50 || '0';
                        
                        demographicsSection.style.display = 'block';
                    } else {
                        demographicsSection.style.display = 'none';
                    }
                    
                    // Show the modal
                    eventModal.show();
                })
                .catch(error => {
                    console.error('Error loading event details:', error);
                    alert('An error occurred while loading event details.');
                });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/domains/csb-mrma.in/csb/resources/views/zonal-coordinator/events/index.blade.php ENDPATH**/ ?>