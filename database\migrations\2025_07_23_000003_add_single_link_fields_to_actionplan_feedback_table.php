<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            // Add single link fields
            $table->string('media_link')->nullable(); // Single media link as text
            $table->string('social_media_link')->nullable(); // Single social media link as text
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            $table->dropColumn([
                'media_link',
                'social_media_link'
            ]);
        });
    }
};
